Stack trace:
Frame         Function      Args
0007FFFFBE10  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBE10, 0007FFFFAD10) msys-2.0.dll+0x1FE8E
0007FFFFBE10  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC0E8) msys-2.0.dll+0x67F9
0007FFFFBE10  000210046832 (000210286019, 0007FFFFBCC8, 0007FFFFBE10, 000000000000) msys-2.0.dll+0x6832
0007FFFFBE10  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBE10  000210068E24 (0007FFFFBE20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC0F0  00021006A225 (0007FFFFBE20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB82120000 ntdll.dll
7FFB81D80000 KERNEL32.DLL
7FFB7F270000 KERNELBASE.dll
7FFB80E70000 USER32.dll
7FFB7FDE0000 win32u.dll
7FFB80E40000 GDI32.dll
7FFB7F880000 gdi32full.dll
7FFB7FE10000 msvcp_win.dll
7FFB7FBD0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB80070000 advapi32.dll
7FFB805C0000 msvcrt.dll
7FFB82030000 sechost.dll
7FFB818A0000 RPCRT4.dll
7FFB7E780000 CRYPTBASE.DLL
7FFB7F660000 bcryptPrimitives.dll
7FFB80030000 IMM32.DLL
