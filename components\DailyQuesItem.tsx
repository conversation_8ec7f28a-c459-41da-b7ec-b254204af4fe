import { Text, View } from 'react-native'
import React from 'react'

interface DailyQuestProps {
   title: string;
   taskCount: number;
   taskPassed: number;
   isPassed?: boolean;
}

const DailyQuesItem: React.FC<DailyQuestProps> = ({title, taskCount, taskPassed, isPassed}) => {
  return (
      <View style={{ padding: 10, borderBottomWidth: 1, borderBottomColor: '#E1E1E1',width: '100%',  maxWidth: 351, marginHorizontal: 'auto'}}>
         <View style={{ display: 'flex', flexDirection: 'row', height: 53, justifyContent: 'space-between'}}>
            <View style={{display: 'flex', flexDirection: 'column', justifyContent: 'space-between'}}>
               <Text style={{
                  fontWeight: 600,
                  fontSize: 16,
                  color: '#242B35'
               }}>{title}</Text>
               <View style={{backgroundColor: '#F1C644', width: 216, height: 19, borderRadius: 25, alignItems: 'center'}}>
                  {/* Progress Bar */}
                  <Text style={{fontWeight: 700, fontSize: 14, color: '#fff', marginHorizontal: 'auto'}}>{taskPassed}/{taskCount}</Text>
               </View>
            </View>

            <Text 
               style={{
                  fontWeight: 700,
                  fontSize: 20,
                  color: '#ED8A19',
                  marginVertical: 'auto'
            }}>
               10 XP
            </Text>
         </View>
      </View>
  )
}

export default DailyQuesItem