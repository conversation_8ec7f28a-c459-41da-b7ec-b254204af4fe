import { Text, View } from 'react-native'
import React from 'react'

interface DailyQuestProps {
   title: string;
   taskCount: number;
   taskPassed: number;
   isPassed?: boolean;
}

const DailyQuesItem: React.FC<DailyQuestProps> = ({title, taskCount, taskPassed, isPassed}) => {
   const progressPercentage = taskCount > 0 ? (taskPassed / taskCount) * 100 : 0;
   const isCompleted = isPassed || taskPassed >= taskCount;

   return (
      <View style={{
         padding: 10,
         borderBottomWidth: 1,
         borderBottomColor: '#E1E1E1',
         width: '100%',
         maxWidth: 351,
         marginHorizontal: 'auto',
         backgroundColor: isCompleted ? '#F8F8F8' : '#fff',
         opacity: isCompleted ? 0.8 : 1,
      }}>
         <View style={{ display: 'flex', flexDirection: 'row', height: 53, justifyContent: 'space-between'}}>
            <View style={{display: 'flex', flexDirection: 'column', justifyContent: 'space-between', flex: 1}}>
               <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Text style={{
                     fontWeight: 600,
                     fontSize: 16,
                     color: '#242B35',
                     flex: 1,
                  }}>{title}</Text>
                  {isCompleted && (
                     <Text style={{ color: '#4CAF50', fontSize: 16, marginLeft: 8 }}>✓</Text>
                  )}
               </View>

               <View style={{
                  backgroundColor: '#E1E1E1',
                  width: 216,
                  height: 19,
                  borderRadius: 25,
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative',
               }}>
                  {/* Progress Bar Fill */}
                  <View style={{
                     position: 'absolute',
                     left: 0,
                     top: 0,
                     backgroundColor: isCompleted ? '#4CAF50' : '#F1C644',
                     height: 19,
                     borderRadius: 25,
                     width: `${Math.min(progressPercentage, 100)}%`,
                  }} />

                  {/* Progress Text */}
                  <Text style={{
                     fontWeight: 700,
                     fontSize: 14,
                     color: '#fff',
                     zIndex: 1,
                  }}>
                     {taskPassed}/{taskCount}
                  </Text>
               </View>
            </View>

            <View style={{ justifyContent: 'center', alignItems: 'center', marginLeft: 10 }}>
               <Text
                  style={{
                     fontWeight: 700,
                     fontSize: 20,
                     color: isCompleted ? '#4CAF50' : '#ED8A19',
               }}>
                  10 XP
               </Text>
               {isCompleted && (
                  <Text style={{ fontSize: 10, color: '#4CAF50', marginTop: 2 }}>
                     Получено
                  </Text>
               )}
            </View>
         </View>
      </View>
   )
}

export default DailyQuesItem