# API Документация - Система Достижений (Мобильное Приложение)

## Обзор

API для мобильного приложения позволяет пользователям просматривать свои достижения, отслеживать прогресс и получать уведомления о новых достижениях.

## Аутентификация

Все эндпоинты требуют валидный JWT токен в заголовке:
```
Authorization: Bearer <your_jwt_token>
```

## Базовый URL

```
https://your-api-domain.com/v1
```

## Эндпоинты для пользователей

### 1. Получение всех доступных достижений

**GET** `/v1/achievements-all`

Возвращает список всех активных достижений в системе.

#### Ответ:
```json
{
  "achievements": [
    {
      "id": 1,
      "name": "Первые шаги",
      "description": "Пройдите первый урок",
      "type": "lessons",
      "target": 1,
      "icon": "🎯",
      "reward": 10,
      "category": "progress",
      "difficulty": "easy"
    },
    {
      "id": 2,
      "name": "Словарный запас",
      "description": "Выучите 5 новых слов",
      "type": "words",
      "target": 5,
      "icon": "📝",
      "reward": 15,
      "category": "vocabulary",
      "difficulty": "easy"
    }
  ]
}
```

### 2. Получение достижений пользователя

**GET** `/v1/achievements/{user_id}`

Возвращает все достижения конкретного пользователя с информацией о прогрессе.

#### Параметры URL:
- `user_id` (integer) - ID пользователя

#### Ответ:
```json
{
  "user_achievements": [
    {
      "id": 1,
      "achievement_id": 1,
      "user_id": 123,
      "progress": 1,
      "achieved": true,
      "achieved_at": "2025-08-05T10:30:00Z",
      "reward_claimed": true,
      "achievement": {
        "id": 1,
        "name": "Первые шаги",
        "description": "Пройдите первый урок",
        "type": "lessons",
        "target": 1,
        "icon": "🎯",
        "reward": 10,
        "category": "progress",
        "difficulty": "easy"
      }
    },
    {
      "id": 2,
      "achievement_id": 2,
      "user_id": 123,
      "progress": 3,
      "achieved": false,
      "achieved_at": null,
      "reward_claimed": false,
      "achievement": {
        "id": 2,
        "name": "Словарный запас",
        "description": "Выучите 5 новых слов",
        "type": "words",
        "target": 5,
        "icon": "📝",
        "reward": 15,
        "category": "vocabulary",
        "difficulty": "easy"
      }
    }
  ]
}
```

### 3. Обновление прогресса достижения (Legacy)

**PATCH** `/v1/achievements`

Обновляет прогресс пользователя по конкретному достижению.

#### Тело запроса:
```json
{
  "user_id": 123,
  "achievement_id": 2,
  "progress": 1
}
```

#### Ответ:
```json
{
  "result": {
    "achievement_name": "Словарный запас",
    "newly_achieved": false,
    "previous_progress": 3,
    "current_progress": 4,
    "target": 5,
    "reward": 15
  }
}
```

## Эндпоинты для отслеживания активности

### 4. Отметка изученных слов

**POST** `/v1/word/learned`

Отмечает слова как изученные и автоматически обновляет соответствующие достижения.

#### Тело запроса:
```json
{
  "user_id": 123,
  "word_ids": [1, 2, 3]
}
```

#### Ответ:
```json
{
  "message": "words marked as learned",
  "words_count": 3
}
```

### 5. Отметка ответа на вопрос

**POST** `/v1/question/answered`

Отмечает ответ на вопрос и обновляет достижения за точность и количество вопросов.

#### Тело запроса:
```json
{
  "user_id": 123,
  "question_id": 45,
  "is_correct": true
}
```

#### Ответ:
```json
{
  "message": "question answer recorded"
}
```

### 6. Сохранение прогресса урока

**POST** `/v1/progress/save`

Сохраняет прогресс прохождения урока и автоматически обновляет достижения.

#### Тело запроса:
```json
{
  "user_id": 123,
  "module_id": 5,
  "mistaken_question_ids": [1, 3],
  "time": 120
}
```

#### Ответ:
```json
{
  "progress": {
    "id": 1,
    "user_id": 123,
    "module_id": 5,
    "mistaken_question_ids": [1, 3],
    "time": 120,
    "created_at": "2025-08-05T10:30:00Z"
  }
}
```

## Структура данных

### Achievement (Достижение)
```json
{
  "id": 1,
  "name": "Название достижения",
  "description": "Описание достижения",
  "type": "lessons|words|streak|modules|questions|accuracy|speed|consecutive",
  "target": 10,
  "icon": "🎯",
  "reward": 50,
  "category": "progress|vocabulary|consistency|accuracy|speed|general",
  "difficulty": "easy|medium|hard"
}
```

### UserAchievement (Достижение пользователя)
```json
{
  "id": 1,
  "achievement_id": 1,
  "user_id": 123,
  "progress": 5,
  "achieved": false,
  "achieved_at": "2025-08-05T10:30:00Z",
  "reward_claimed": false,
  "achievement": { /* объект Achievement */ }
}
```

## Автоматическое обновление достижений

Система автоматически отслеживает следующие действия пользователя:

### При сохранении прогресса урока:
- ✅ Обновляются достижения типа `lessons`
- ✅ Обновляются достижения типа `modules` 
- ✅ Обновляются достижения типа `questions`

### При отметке изученных слов:
- ✅ Обновляются достижения типа `words`

### При ответе на вопросы:
- ✅ Обновляются достижения типа `questions`
- ✅ Обновляются достижения типа `consecutive` (при правильных ответах)
- ✅ Сбрасываются достижения типа `consecutive` (при неправильных ответах)

## Уведомления о достижениях

При получении нового достижения пользователь автоматически получает push-уведомление с информацией:

### Структура уведомления:
```json
{
  "title": "🎯 Прогресс в обучении!",
  "body": "Поздравляем! Вы получили достижение 'Первые шаги' Награда: 10 очков!",
  "data": {
    "type": "achievement",
    "achievement_id": "1",
    "achievement_name": "Первые шаги",
    "category": "progress",
    "difficulty": "easy",
    "reward": "10",
    "icon": "🎯"
  }
}
```

### Типы уведомлений по категориям:

| Категория | Заголовок | Иконка |
|-----------|-----------|--------|
| `progress` | 🎯 Прогресс в обучении! | 📈 |
| `vocabulary` | 📚 Словарный запас! | 📝 |
| `consistency` | 🔥 Постоянство! | 💪 |
| `accuracy` | 🎯 Точность! | ✨ |
| `speed` | ⚡ Скорость! | 🚀 |
| `general` | 🏆 Новое достижение! | 🏆 |

## Примеры интеграции

### React Native / Flutter

```javascript
// Получение достижений пользователя
const getUserAchievements = async (userId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/v1/achievements/${userId}`, {
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    return data.user_achievements;
  } catch (error) {
    console.error('Error fetching achievements:', error);
  }
};

// Отметка изученных слов
const markWordsAsLearned = async (userId, wordIds) => {
  try {
    const response = await fetch(`${API_BASE_URL}/v1/word/learned`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        user_id: userId,
        word_ids: wordIds
      })
    });
    
    return await response.json();
  } catch (error) {
    console.error('Error marking words as learned:', error);
  }
};
```

### Обработка push-уведомлений

```javascript
// Обработка уведомления о достижении
const handleAchievementNotification = (notification) => {
  if (notification.data.type === 'achievement') {
    const achievementData = {
      id: notification.data.achievement_id,
      name: notification.data.achievement_name,
      category: notification.data.category,
      reward: parseInt(notification.data.reward),
      icon: notification.data.icon
    };
    
    // Показать модальное окно с поздравлением
    showAchievementModal(achievementData);
    
    // Обновить список достижений пользователя
    refreshUserAchievements();
  }
};
```

## Рекомендации по UX

### Отображение прогресса:
- Используйте прогресс-бары для незавершенных достижений
- Показывайте процент выполнения: `progress / target * 100`
- Группируйте достижения по категориям

### Мотивация пользователей:
- Показывайте ближайшие к завершению достижения
- Выделяйте новые достижения специальными эффектами
- Отображайте общее количество заработанных очков

### Геймификация:
- Добавьте анимации при получении достижений
- Используйте звуковые эффекты для положительного подкрепления
- Создайте раздел "Витрина достижений" в профиле пользователя
