import { StyleSheet, Text, View } from "react-native";
import React from "react";
import { Theory } from "../../types/module";
import ArrowUpDownSvg from "../../assets/svg/ArrowUpDownSvg";

export default function ThoeryItem({ theory }: { theory: Theory }) {
   return (
      <View
         style={{
            width: "87%",
            // height: "100%",
            paddingVertical: 14,
            borderRadius: 10,
            backgroundColor: "#bfebf1",
            marginHorizontal: "auto",
         }}
      >
         <View style={{ marginVertical: "auto" }}>
            <View
               style={{
                  width: "95%",
                  marginHorizontal: "auto",
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: 10,
               }}
            >
               <Text
                  style={{
                     fontWeight: 600,
                     fontSize: 12,
                     color: "#242B35",
                  }}
               >
                  {theory.title}
               </Text>

               <ArrowUpDownSvg />
            </View>
            <Text
               style={{
                  fontWeight: 500,
                  fontSize: 12,
                  color: "#242B35",
                  width: "95%",
                  marginHorizontal: "auto",
               }}
            >
               {theory.description}
            </Text>

            <View
               style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "space-between",
                  width: "90%",
                  marginHorizontal: "auto",
                  alignItems: "center",
               }}
            >
               {theory.tags.map((tag, index) => {
                  let color = "#F7AD3A";
                  if (index == 1) {
                     color = "#34C759";
                  } else if (index == 2) {
                     color = "#007AFF";
                  }
                  return (
                     <View style={{ flexDirection: "row", alignItems: "center" }} key={index}>
                        <View
                           style={{
                              borderRadius: 5,
                              backgroundColor: color,
                              marginVertical: 20,
                              marginRight: 12,
                           }}
                        >
                           <Text
                              style={{
                                 padding: 5,
                                 fontWeight: 500,
                                 fontSize: 12,
                                 color: "#fff",
                              }}
                           >
                              {tag}
                           </Text>
                        </View>
                        {index != theory.tags.length - 1 ? (
                           <Text
                              style={{
                                 color: "#000000",
                                 fontWeight: 500,
                                 fontSize: 12,
                              }}
                           >
                              +
                           </Text>
                        ) : (
                           ""
                        )}
                     </View>
                  );
               })}
            </View>
            <View
               style={{
                  width: "95%",
                  marginHorizontal: "auto",
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: 10,
               }}
            >
               <Text
                  style={{
                     fontWeight: 600,
                     fontSize: 12,
                     color: "#242B35",
                  }}
               >
                  Примеры:
               </Text>

               <View style={{ opacity: 0 }}>
                  <ArrowUpDownSvg />
               </View>
            </View>

            {theory.examples.map((example, index) => {
               return (
                  <View
                     key={index}
                     style={{
                        width: "95%",
                        marginHorizontal: "auto",
                        // height: 52,
                        borderRadius: 5,
                        backgroundColor: "#fff",
                        marginBottom: 8,
                     }}
                  >
                     <View style={{ margin: 8 }}>
                        <Text
                           style={{
                              fontWeight: 600,
                              fontSize: 12,
                              color: "#242B35",
                              marginBottom: 5,
                           }}
                        >
                           {example.kaz_plaintext}
                        </Text>
                        <Text
                           style={{
                              fontWeight: 600,
                              fontSize: 10,
                              color: "#242B35",
                           }}
                        >
                           {example.rus_plaintext}
                        </Text>
                     </View>
                  </View>
               );
            })}
         </View>
      </View>
   );
}

const styles = StyleSheet.create({});
