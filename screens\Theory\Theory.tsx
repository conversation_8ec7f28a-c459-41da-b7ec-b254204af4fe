import { ScrollView, <PERSON>Bar, StyleSheet, Text, View } from "react-native";
import React, { useEffect, useState } from "react";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";
import ToggleSwitch from "./ToggleSwitch";
import ArrowUpDownSvg from "../../assets/svg/ArrowUpDownSvg";
import { Theory as theory } from "../../types/module";
import { GetRequest } from "../../utils/service";
import ThoeryItem from "./ThoeryItem";

export default function Theory() {
   const [theories, setTheories] = useState<theory[]>([]);
   const [isLoading, setIsLoading] = useState(true);

   useEffect(() => {
      const fetchData = async () => {
         try {
            const response = await GetRequest(`/v1/theory/all`);

            setTheories(response.theories);
            setIsLoading(false);
         } catch (error) {
            console.log(error);
            setIsLoading(false);
         }
      };

      fetchData();
   }, []);

   return (
      <SafeAreaProvider style={{ backgroundColor: "#fff" }}>
         <StatusBar barStyle="light-content" />
         <SafeAreaView>
            <ScrollView>
               <Text
                  style={{
                     fontWeight: 700,
                     fontSize: 20,
                     color: "#242B35",
                     marginHorizontal: "auto",
                     textAlign: "center",
                  }}
               >
                  Теория
               </Text>

               <ToggleSwitch />

               <View style={{ width: 334, marginHorizontal: "auto" }}>
                  <Text
                     style={{
                        color: "#242B35",
                        fontWeight: 700,
                        fontSize: 16,
                        marginBottom: 17,
                        marginTop: 27,
                     }}
                  >
                     Курс 1
                  </Text>
               </View>

               {theories.map((theory) => {
                  return <ThoeryItem key={theory.id} theory={theory} />;
               })}
            </ScrollView>
         </SafeAreaView>
      </SafeAreaProvider>
   );
}
