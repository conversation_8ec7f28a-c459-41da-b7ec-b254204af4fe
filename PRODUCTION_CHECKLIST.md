# 🚀 Production Checklist для Push-уведомлений

## ✅ Готово к продакшн

### 1. **Expo Push Service**
- ✅ Интегрирован и протестирован
- ✅ Работает с Expo Go токенами
- ✅ Автоматическое определение типа токена
- ✅ Детальное логирование

### 2. **Firebase FCM**
- ✅ Настроен как fallback
- ✅ Работает с FCM токенами
- ✅ Интегрирован в общую систему

### 3. **API Endpoints**
- ✅ `/v1/notifications/send` - основной endpoint
- ✅ `/v1/notifications/test` - тестовый endpoint (требует авторизацию)
- ✅ `/v1/notifications/debug` - отключается в продакшн

## ⚠️ Требует настройки для продакшн

### 1. **CORS настройки**
```go
// В cmd/api/cors.go замените:
AllowedOrigins: []string{
    "https://your-frontend-domain.com",  // ВАШ ПРОДАКШН ДОМЕН
    "https://admin.your-domain.com",     // АДМИН ПАНЕЛЬ
    // Удалите localhost домены для продакшн
},
```

### 2. **Environment переменные**
```bash
# Установите в продакшн:
ENV=production

# Для отключения debug endpoints
```

### 3. **Мониторинг**
- ✅ Логи отправляются в MongoDB
- ✅ Метрики доступны на `/debug/vars`
- ⚠️ Настройте алерты на ошибки отправки

### 4. **Безопасность**
- ✅ Debug endpoints отключаются в продакшн
- ✅ CORS настроен для конкретных доменов
- ⚠️ Убедитесь, что serviceAccount.json защищен

## 📱 Expo приложения в продакшн

### **Expo Go (разработка)**
- Токены: `ExponentPushToken[...]`
- Сервис: Expo Push Service
- ✅ Готово

### **Standalone приложения (продакшн)**

#### **EAS Build (рекомендуется)**
```bash
# Установите EAS CLI
npm install -g @expo/eas-cli

# Настройте проект
eas build:configure

# Соберите для продакшн
eas build --platform all
```

#### **Токены в продакшн:**
- **Android**: Могут быть FCM-подобными, но отправляются через Expo
- **iOS**: Остаются `ExponentPushToken[...]` или APNs токены
- ✅ Ваша система автоматически определит тип

### **Получение токенов в приложении:**
```javascript
import * as Notifications from 'expo-notifications';

// В продакшн приложении
const token = await Notifications.getExpoPushTokenAsync({
  projectId: 'your-expo-project-id', // Из app.json
});

// Отправьте token.data на ваш сервер
console.log('Push token:', token.data);
```

## 🔧 Настройка мониторинга

### **1. Логи в MongoDB**
```javascript
// Поиск ошибок push-уведомлений
db.logs.find({
  "level": "ERROR",
  "properties.service": { $exists: true }
}).sort({ time: -1 })
```

### **2. Метрики**
- Доступны на: `https://your-domain.com/debug/vars`
- Мониторьте: `total_requests_received`, `total_responses_sent_by_status`

### **3. Алерты**
Настройте алерты на:
- Высокий процент ошибок отправки
- Недоступность Expo Push Service
- Ошибки Firebase

## 🚀 Деплой в продакшн

### **1. Обновите CORS**
```bash
# Замените домены в cmd/api/cors.go
```

### **2. Установите ENV**
```bash
export ENV=production
```

### **3. Пересоберите**
```bash
docker-compose build app
docker-compose up -d
```

### **4. Проверьте**
```bash
# Debug endpoint должен вернуть 404 в продакшн
curl https://your-domain.com/v1/notifications/debug

# Основные endpoints должны работать
curl https://your-domain.com/v1/healthcheck
```

## 📊 Ожидаемая производительность

### **Expo Push Service**
- ✅ До 100 токенов за запрос
- ✅ Высокая надежность
- ✅ Автоматические retry

### **Firebase FCM**
- ✅ До 1000 токенов за запрос
- ✅ Высокая производительность
- ✅ Детальная аналитика

## 🎯 Итог

Ваша система push-уведомлений **готова к продакшн** с минимальными изменениями:

1. **Обновите CORS домены**
2. **Установите ENV=production**
3. **Пересоберите Docker образ**
4. **Настройте мониторинг**

Все остальное уже готово! 🎉
