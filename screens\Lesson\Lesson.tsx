import { Alert, Platform, StyleSheet, Text, View } from "react-native";
import React, { useEffect, useState } from "react";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";
import LessonLoading from "./LessonLoading";
import NewWordsQuestion from "./new_words/NewWordsQuestion";
import GrammarQuestion from "./grammar/GrammarQuestion";
import { __mock_complex_lessons } from "./mock_data";
import PracticeQuestion from "./practice/PracticeQuestion";
import { useNavigation } from "@react-navigation/native";
import { StatusBar } from "expo-status-bar";
import { GetRequest, PostRequest } from "../../utils/service";
import { Module, Question, Result } from "../../types/module";
import { useAuth } from "../../context/AuthContext";

export default function Lesson({ route }: { route: any }) {
   const { navigate } = useNavigation();
   const { authState } = useAuth();

   const [module, setModule] = useState<Module>();
   const [newWordsQuestions, setNewWordsQuestions] = useState<Question[]>([]);
   const [grammarQuestions, setGrammarQuestions] = useState<Question[]>([]);
   const [practiceQuestions, setPracticeQuestions] = useState<Question[]>([]);
   const [currentLessonPart, setCurrentLessonPart] = useState("new-words");

   const [moduleResult, setModuleResult] = useState<Result>({
      user_id: authState.user?.id ?? -1,
      module_id: module?.id ?? -1,
      mistaken_questions_ids: [],
      time: "-",
   });
   const [startTime, setStartTime] = useState<number>(Date.now());

   useEffect(() => {
      const fetchData = async () => {
         try {
            const response = await GetRequest(`/v1/module?id=${route.params.id}`);
            setModule(response.module);

            const nwq = response.module.questions.filter((q: Question) => q.type === "kz-word-to-ru-word");
            const gmq = response.module.questions.filter((q: Question) => q.type === "build-sentence");
            const ptq = response.module.questions.filter((q: Question) => q.type === "by-letter");

            setNewWordsQuestions(nwq ?? []);
            setGrammarQuestions(gmq ?? []);
            setPracticeQuestions(ptq ?? []);
         } catch (error) {
            console.log(error);
         }
      };

      fetchData();
   }, []);

   const saveProgressToServer = async (result: Result) => {
      try {
         await PostRequest(`/v1/progress/save`, {
            user_id: authState.user?.id,
            module_id: module?.id,
            mistaken_question_ids: result.mistaken_questions_ids,
            time: result.time,
         });
      } catch (error) {
         console.log(error);
         Alert.alert("Предупреждение", "Не удалось сохранить прогресс!");
      }
   };

   const handleFinishPart = async (result: Result) => {
      if (currentLessonPart == "new-words") {
         setCurrentLessonPart("grammar");
         setIsLoading(true);

         let tempResult: Result = {
            user_id: authState.user?.id ?? -1,
            module_id: module?.id ?? -1,
            mistaken_questions_ids: [...moduleResult?.mistaken_questions_ids, ...result.mistaken_questions_ids],
            time: "-",
         };
         setModuleResult(tempResult);

         return;
      }

      if (currentLessonPart == "grammar") {
         setCurrentLessonPart("practice");
         setIsLoading(true);

         let tempResult: Result = {
            user_id: authState.user?.id ?? -1,
            module_id: module?.id ?? -1,
            mistaken_questions_ids: [...moduleResult?.mistaken_questions_ids, ...result.mistaken_questions_ids],
            time: "-",
         };
         setModuleResult(tempResult);
         return;
      }

      if (currentLessonPart == "practice") {
         const endTime = Date.now();
         const durationMilliseconds = endTime - startTime;

         // Форматируем время как HH:mm:ss
         const hours = Math.floor(durationMilliseconds / 3600000);
         const minutes = Math.floor((durationMilliseconds % 3600000) / 60000);
         const seconds = Math.floor((durationMilliseconds % 60000) / 1000);
         const formattedTime = `${hours}:${minutes}:${seconds}`;

         let tempResult: Result = {
            user_id: authState.user?.id ?? -1,
            module_id: module?.id ?? -1,
            mistaken_questions_ids: [...moduleResult?.mistaken_questions_ids, ...result.mistaken_questions_ids],
            time: formattedTime,
         };
         setModuleResult(tempResult);

         await saveProgressToServer(tempResult);

         setIsLoading(true);
         setCurrentLessonPart("done");
         setTimeout(() => {
            setIsLoading(false);
            // @ts-ignore
            navigate("Lesson-Result");
         }, 3000);
      }
   };

   const [isLoading, setIsLoading] = useState(true);

   setTimeout(() => {
      setIsLoading(false);
   }, 3000);

   const renderContent = () => {
      if (isLoading) {
         return <LessonLoading type={currentLessonPart} />;
      }

      if (currentLessonPart == "new-words") {
         return <NewWordsQuestion questions={newWordsQuestions} onFinishPart={handleFinishPart} />;
      }

      if (currentLessonPart == "grammar") {
         return <GrammarQuestion questions={grammarQuestions} onFinishPart={handleFinishPart} />;
      }

      if (currentLessonPart == "practice") {
         return <PracticeQuestion questions={practiceQuestions} onFinishPart={handleFinishPart} />;
      }
   };

   return (
      <SafeAreaProvider style={{ backgroundColor: "#fff" }}>
         <StatusBar style="dark" />
         <SafeAreaView
            style={{
               marginTop: Platform.OS === "android" ? 50 : 0,
            }}
         >
            {renderContent()}
         </SafeAreaView>
      </SafeAreaProvider>
   );
}
