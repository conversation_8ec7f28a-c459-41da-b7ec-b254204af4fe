import Svg, {
   Path,
   G,
   Image,
   Defs,
   Rect,
   Use,
   Pattern,
} from "react-native-svg";

const GirlSvg = () => (
   <Svg width="141" height="139" viewBox="0 0 141 139" fill="none">
      <Rect
         y="-6"
         width="166.802"
         height="164.652"
         fill="url(#pattern0_290_1616)"
      />
      <Defs>
         <Pattern
            id="pattern0_290_1616"
            patternContentUnits="objectBoundingBox"
            width="1"
            height="1"
         >
            <Use transform="scale(0.000402901 0.000408163)" />
         </Pattern>
         <Image id="image0_290_1616" width="2482" height="2450" />
      </Defs>
   </Svg>
);

export default GirlSvg;
