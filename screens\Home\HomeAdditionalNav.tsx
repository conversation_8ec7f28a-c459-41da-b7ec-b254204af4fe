import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React, { useEffect, useRef, useState } from "react";
import ChessSvg from "../../assets/svg/ChessSvg";
import NotificationsSvg from "../../assets/svg/NotificationsSvg";
import { useNavigation } from "@react-navigation/native";
import { AudioPlayer, AudioPlayerRef } from "../../utils/AudioPlayer";
export default function HomeAdditionalNav() {
   const { navigate } = useNavigation();

   return (
      <View style={styles.buttonContainer}>
         <TouchableOpacity
            onPress={() => {
               // @ts-ignore
               navigate("Achievements");
            }}
            style={styles.button}
         >
            <View style={styles.buttonIcon}>
               <ChessSvg />
            </View>

            <Text style={styles.buttonText}>Задания</Text>
         </TouchableOpacity>
         <TouchableOpacity style={styles.button}>
            <View style={styles.buttonIcon}>
               <NotificationsSvg />
            </View>

            <Text style={styles.buttonText}>Уведомления</Text>
         </TouchableOpacity>
      </View>
   );
}

const styles = StyleSheet.create({
   buttonContainer: {
      width: "85%",
      marginHorizontal: "auto",
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      marginTop: 12,
   },
   button: {
      height: 42,
      borderRadius: 6,
      borderWidth: 2.4,
      borderColor: "#E1E1E1",
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 12,
      justifyContent: "center",
   },
   buttonText: {
      fontWeight: "600",
      fontSize: 14,
      color: "#242B35",
   },
   buttonIcon: {
      marginRight: 12,
   },
});
