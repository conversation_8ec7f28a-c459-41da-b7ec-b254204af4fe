import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>man<PERSON><PERSON><PERSON> } from "../../assets/audio";
import { Apteka, Forest, Pillow } from "../../assets/image";

export const __mock_complex_lessons = {
   new_words: [
      {
         id: 1,
         type: "kz-word-to-ru-word",
         variants: [
            {
               id: 5165,
               plaintext: "Емхана",
               rus_plaintext: "Больница",
               url: EmhanaAudio,
               voice_id: 1,
            },
            {
               id: 6158,
               plaintext: "Дәріхана",
               rus_plaintext: "Аптека",
               url: DarihanaAudio,
               voice_id: 1,
            },
            {
               id: 5995,
               plaintext: "Асхана",
               rus_plaintext: "Столовая",
               url: "https://storage.google.com/word/5165",
               voice_id: 1,
            },
         ],
         correct_answer: {
            id: 6158,
            imageUrl: Apteka,
            plaintext: "Дәріхана",
            rus_plaintext: "А<PERSON>тек<PERSON>",
            url: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
            voice_id: 1,
         },
      },
      {
         id: 2,
         type: "kz-word-to-ru-word",
         variants: [
            {
               id: 1625,
               plaintext: "Мектеп",
               rus_plaintext: "Школа",
               url: "https://storage.google.com/word/5165",
               voice_id: 1,
            },
            {
               id: 2626,
               plaintext: "Жастық",
               rus_plaintext: "Подушка",
               url: Pillow,
               voice_id: 1,
            },
            {
               id: 5691,
               plaintext: "Бесік",
               rus_plaintext: "Колыбель",
               url: "https://storage.google.com/word/5165",
               voice_id: 1,
            },
         ],
         correct_answer: {
            id: 2626,
            imageUrl: Pillow,
            plaintext: "Жастық",
            rus_plaintext: "Подушка",
            url: JastyqAudio,
            voice_id: 1,
         },
      },
      {
         id: 3,
         type: "kz-word-to-ru-word",
         variants: [
            {
               id: 1295,
               plaintext: "Орман",
               rus_plaintext: "Лес",
               url: OrmanAudio,
               voice_id: 1,
            },
            {
               id: 1698,
               plaintext: "Аула",
               rus_plaintext: "Двор",
               url: JastyqAudio,
               voice_id: 1,
            },
            {
               id: 91951,
               plaintext: "Аспан",
               rus_plaintext: "Небо",
               url: "https://storage.google.com/word/5165",
               voice_id: 1,
            },
         ],
         correct_answer: {
            id: 1295,
            imageUrl: Forest,
            plaintext: "Орман",
            rus_plaintext: "Лес",
            url: OrmanAudio,
            voice_id: 1,
         },
      },
      {
         id: 4,
         type: "word-pair",
         words: [
            {
               id: 1295,
               imageUrl: "",
               plaintext: "Орман",
               rus_plaintext: "Лес",
               url: OrmanAudio,
               voice_id: 1,
            },
            {
               id: 1698,
               plaintext: "Аула",
               rus_plaintext: "Двор",
               url: JastyqAudio,
               voice_id: 1,
            },
            {
               id: 5691,
               plaintext: "Бесік",
               rus_plaintext: "Колыбель",
               url: "https://storage.google.com/word/5165",
               voice_id: 1,
            },
            {
               id: 6158,
               plaintext: "Дәріхана",
               rus_plaintext: "Аптека",
               url: DarihanaAudio,
               voice_id: 1,
            },
            {
               id: 5995,
               plaintext: "Асхана",
               rus_plaintext: "Столовая",
               url: "https://storage.google.com/word/5165",
               voice_id: 1,
            },
         ],
      },
   ],

   grammar: [
      {
         id: 19519,
         type: "build-sentence",
         words: [
            {
               id: 11105,
               kaz_plaintext: "мен",
               rus_plaintext: "я",
               url: EmhanaAudio,
               voice_id: 1,
            },
            {
               id: 61611661,
               kaz_plaintext: "оқимын",
               rus_plaintext: "читаю",
               url: EmhanaAudio,
               voice_id: 1,
            },
            {
               id: 655616,
               kaz_plaintext: "кітап",
               rus_plaintext: "книга",
               url: EmhanaAudio,
               voice_id: 1,
            },
         ],

         correct_answer: {
            rus_plaintext: "Я читаю книгу",
            kaz_plaintext: "Мен кітап оқимын",
            sequence: [11105, 655616, 61611661],
         },
      },
      {
         id: 122266,
         type: "build-sentence",
         words: [
            {
               id: 110043,
               kaz_plaintext: "футбол",
               rus_plaintext: "футбол",
               url: EmhanaAudio,
               voice_id: 1,
            },
            {
               id: 151177813,
               kaz_plaintext: "ойнайды",
               rus_plaintext: "играет",
               url: EmhanaAudio,
               voice_id: 1,
            },
            {
               id: 823476289,
               kaz_plaintext: "Арман",
               rus_plaintext: "Арман",
               url: EmhanaAudio,
               voice_id: 1,
            },
         ],

         correct_answer: {
            kaz_plaintext: "Арман футбол ойнайды",
            rus_plaintext: "Арман играет футбол",
            sequence: [823476289, 110043, 151177813],
         },
      },
      {
         id: 196166,
         type: "build-sentence",
         words: [
            {
               id: 93855647,
               kaz_plaintext: "айтады",
               rus_plaintext: "поет",
               url: EmhanaAudio,
               voice_id: 1,
            },
            {
               id: 428857635,
               kaz_plaintext: "Айнұр",
               rus_plaintext: "Айнур",
               url: EmhanaAudio,
               voice_id: 1,
            },
            {
               id: 13885674,
               kaz_plaintext: "ән",
               rus_plaintext: "песня",
               url: EmhanaAudio,
               voice_id: 1,
            },
         ],

         correct_answer: {
            kaz_plaintext: "Айнұр ән айтады",
            rus_plaintext: "Айнур поет песню",
            sequence: [428857635, 13885674, 93855647],
         },
      },
   ],
};
