import React from "react";
import { TouchableOpacity, Text, StyleSheet, View } from "react-native";

interface ButtonProps {
   title: string;
   handlePress: () => void;
}

const PracticeHeaderButton: React.FC<ButtonProps> = ({
   title,
   handlePress,
}) => {
   return (
      <TouchableOpacity onPress={handlePress} style={styles.buttonContainer}>
         <View
            style={{
               ...styles.container,
               backgroundColor: "#fff",
               shadowColor: "#BCDEED", // Цвет тени, активная и неактивная
               shadowOffset: { width: 0, height: 4 }, // Смещение тени
               shadowOpacity: 1, // Прозрачность тени
               shadowRadius: 0, // Радиус размытия
               elevation: 4, // Только для Android
            }}
         >
            <Text style={{ ...styles.title, color: "#242B35" }}>{title}</Text>
         </View>
      </TouchableOpacity>
   );
};

const styles = StyleSheet.create({
   buttonContainer: {
      marginHorizontal: "auto",
   },
   container: {
      borderRadius: 12,
      width: 134,
      height: 25,
      justifyContent: "center",
      alignItems: "center",
   },
   title: {
      fontWeight: "700",
      color: "#242B35",
      fontSize: 10,
   },
});

export default PracticeHeaderButton;
