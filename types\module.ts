export interface Module {
   id: number;
   name: string;
   theory_ids?: number[];
   question_ids?: number[];
   questions: Question[];
   theories?: Theory[];
   level: number;
   pre_requisite_ids?: number[];
   created_at?: string;
}

export interface Question {
   id: number;
   type: string;
   words: any[];
   correct_answer: any;
   image_url?: string;
   created_at?: string;
}

export interface Theory {
   id: number;
   title: string;
   description: string;
   module_id: number;
   tags: string[];
   examples_ids: number[];
   examples: Example[];
   created_at: string;
}

export interface Example {
   kaz_plaintext: string;
   rus_plaintext: string;
}

export interface Achievement {
   id: number;
   name: string;
   description: string;
   type: string;
   target: number;
   progress: number;
   image: string;
   text_color: string;
   bg_color: string;
   created_at: string;
   updated_at: string;
}

export interface Result {
   user_id: number;
   module_id: number;
   mistaken_questions_ids: number[];
   time: string;
}
