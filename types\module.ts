export interface Module {
   id: number;
   name: string;
   theory_ids?: number[];
   question_ids?: number[];
   questions: Question[];
   theories?: Theory[];
   level: number;
   pre_requisite_ids?: number[];
   created_at?: string;
}

export interface Question {
   id: number;
   type: string;
   words: any[];
   correct_answer: any;
   image_url?: string;
   created_at?: string;
}

export interface Theory {
   id: number;
   title: string;
   description: string;
   module_id: number;
   tags: string[];
   examples_ids: number[];
   examples: Example[];
   created_at: string;
}

export interface Example {
   kaz_plaintext: string;
   rus_plaintext: string;
}

export interface Achievement {
   id: number;
   name: string;
   description: string;
   type: 'lessons' | 'words' | 'streak' | 'modules' | 'questions' | 'accuracy' | 'speed' | 'consecutive';
   target: number;
   icon: string;
   reward: number;
   category: 'progress' | 'vocabulary' | 'consistency' | 'accuracy' | 'speed' | 'general';
   difficulty: 'easy' | 'medium' | 'hard';
   is_active: boolean;
   metadata?: Record<string, any>;
   created_at: string;
   updated_at: string;
   // UI specific fields (computed)
   progress?: number;
   image?: string;
   text_color?: string;
   bg_color?: string;
}

export interface UserAchievement {
   id: number;
   achievement_id: number;
   user_id: number;
   progress: number;
   achieved: boolean;
   achieved_at?: string;
   reward_claimed: boolean;
   achievement: Achievement;
}

export interface DailyQuest {
   id: number;
   title: string;
   description: string;
   type: 'lessons' | 'words' | 'questions' | 'modules' | 'streak';
   target: number;
   reward: number;
   icon: string;
   is_active: boolean;
   created_at: string;
   updated_at: string;
}

export interface UserDailyQuest {
   id: number;
   quest_id: number;
   user_id: number;
   progress: number;
   completed: boolean;
   completed_at?: string;
   reward_claimed: boolean;
   quest: DailyQuest;
}

export interface Result {
   user_id: number;
   module_id: number;
   mistaken_questions_ids: number[];
   time: string;
}
