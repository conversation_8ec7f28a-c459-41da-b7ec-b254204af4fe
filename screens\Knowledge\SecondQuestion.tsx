import React, { useEffect, useState } from "react";
import { Text, View } from "react-native";
import MultiChoiceButton from "../../components/ui/MultiChoiceButton";
import { useDispatch, useSelector } from "react-redux";
import { setValid } from "../../store/slices/buttonSlice";

export default function SecondQuestion() {
   const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
   const dispatch = useDispatch();

   const mockMSQ = [
      {
         title: "Изучить культуру Казахстана",
      },
      {
         title: "Сделать карьеру",
      },
      {
         title: "Разговаривать с носителями",
      },
      {
         title: "Улучшить оценки",
      },
      {
         title: "Просто для себя",
      },
   ];

   if (selectedOptions.length == 0) {
      dispatch(setValid(false));
   }

   const handleOptionPress = (title: string) => {
      setSelectedOptions((prev) => {
         if (prev.includes(title)) {
            // Если элемент уже выбран, удаляем его

            return prev.filter((item) => item !== title);
         } else {
            // Если элемент не выбран, добавляем его
            dispatch(setValid(true));
            return [...prev, title];
         }
      });
   };

   return (
      <View>
         <Text
            style={{
               color: "#8F8F91",
               fontWeight: "400",
               fontSize: 16,
               marginHorizontal: "auto",
               marginBottom: 16,
            }}
         >
            Выберите все варианты
         </Text>
         <View style={{ height: 341, justifyContent: "space-between" }}>
            {mockMSQ.map((item, index) => {
               return (
                  <MultiChoiceButton
                     key={index}
                     handlePress={() => handleOptionPress(item.title)}
                     title={item.title}
                     isPicked={selectedOptions.includes(item.title)}
                  />
               );
            })}
         </View>
      </View>
   );
}
