import { Audio } from "expo-av";
import { CorrectAudio, WrongAudio } from "../assets/audio";

export const PlayWrongSound = async () => {
    const { sound } = await Audio.Sound.createAsync(
       WrongAudio
    );
    await sound.playAsync();


    sound.setOnPlaybackStatusUpdate((status) => {
       // @ts-ignore
       if (status.didJustFinish) {
          sound.unloadAsync();
       }
    });
 };

export const PlayCorrectSound = async () => {
    const { sound } = await Audio.Sound.createAsync(
       CorrectAudio
    );
    await sound.playAsync();


    sound.setOnPlaybackStatusUpdate((status) => {
       // @ts-ignore
       if (status.didJustFinish) {
          sound.unloadAsync();
       }
    });
 };

 