import { BackHandler, Image, Modal, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React, { useEffect, useState } from "react";
import VariantButton from "../../../components/ui/VariantButton";
import ChoiceRequiredButton from "../../../components/ui/ChoiceRequiredButton";
import MessageSvg from "../../../assets/svg/MessageSvg";
import XSvg from "../../../assets/svg/xSvg";
import ProgressBar from "../../../components/ui/ProgressBar";
import LessonAnswerBottom from "../LessonAnswerBottom";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import QuestionPairWords from "./QuestionPairWords";
import { PlayCorrectSound, PlayWrongSound } from "../../../utils/PlaySound";
import { Question, Result } from "../../../types/module";

export default function NewWordsQuestion({
   onFinishPart,
   questions,
}: {
   onFinishPart: (result: Result) => void;
   questions: Question[];
}) {
   const { navigate } = useNavigation();

   useFocusEffect(
      React.useCallback(() => {
         const onBackPress = () => {
            setModalVisible(true);
            return true;
         };

         const subscription = BackHandler.addEventListener("hardwareBackPress", onBackPress);

         return () => subscription.remove();
      }, [])
   );

   const [mockQuestions, setMockQuestions] = useState(questions);
   const totalLen = questions.length;

   const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
   const [mistakensQIds, setMistakenQIds] = useState<number[]>([]);
   const [progress, setProgress] = useState(0);

   const handleNextQuestion = () => {
      if (currentQuestionIndex + 1 == mockQuestions.length) {
         const result: Result = {
            user_id: -1,
            module_id: -1,
            mistaken_questions_ids: mistakensQIds,
            time: "-",
         };

         onFinishPart(result);
         // @ts-ignore
         navigate("Lesson");
         return;
      }
      setCurrentQuestionIndex((prev) => prev + 1);
   };

   const handleSaveResult = (isCorrect: boolean) => {
      if (isCorrect) {
         setProgress(progress + 100 / totalLen);
      }

      if (!isCorrect) {
         const incorrectQuestion = mockQuestions[currentQuestionIndex];
         setMockQuestions([...mockQuestions, incorrectQuestion]);

         setMistakenQIds([...mistakensQIds, mockQuestions[currentQuestionIndex].id]);
      }
   };

   const [modalVisible, setModalVisible] = useState(false);

   return (
      <View>
         <View
            style={{
               marginTop: 25,
               display: "flex",
               flexDirection: "row",
               justifyContent: "space-between",
               width: "90%",
               marginHorizontal: "auto",
               alignItems: "center",
            }}
         >
            <TouchableOpacity
               onPress={() => {
                  setModalVisible(true);
               }}
            >
               <XSvg />
            </TouchableOpacity>
            <ProgressBar height={16} progress={progress} />
         </View>
         {mockQuestions[currentQuestionIndex].type == "kz-word-to-ru-word" ? (
            <QuestionContent
               onNextQuestion={handleNextQuestion}
               onSaveResult={handleSaveResult}
               question={mockQuestions[currentQuestionIndex]}
            />
         ) : (
            ""
         )}

         {mockQuestions[currentQuestionIndex].type == "word-pair" ? (
            <QuestionPairWords
               onNextQuestion={handleNextQuestion}
               onSaveResult={handleSaveResult}
               question={mockQuestions[currentQuestionIndex]}
            />
         ) : (
            ""
         )}

         <Modal
            animationType="fade"
            statusBarTranslucent={true}
            transparent={true}
            presentationStyle="overFullScreen"
            visible={modalVisible}
            onRequestClose={() => {
               setModalVisible(false);
            }}
         >
            <View style={styles.modalContainer}>
               <View style={styles.modalContent}>
                  <Text style={styles.modalText}>Ваш прогресс не сохранится. Уверены что хотите покинуть урок?</Text>
                  <View style={styles.modalButtons}>
                     <TouchableOpacity style={styles.button} onPress={() => setModalVisible(false)}>
                        <Text style={styles.buttonText}>Нет</Text>
                     </TouchableOpacity>
                     <TouchableOpacity
                        style={styles.button}
                        onPress={() => {
                           setModalVisible(false);
                           // @ts-ignore
                           navigate("Home");
                        }}
                     >
                        <Text style={styles.buttonText}>Да</Text>
                     </TouchableOpacity>
                  </View>
               </View>
            </View>
         </Modal>
      </View>
   );
}

const QuestionContent = ({
   question,
   onNextQuestion,
   onSaveResult,
}: {
   question: Question;
   onNextQuestion: () => void;
   onSaveResult: (isCorrect: boolean) => void;
}) => {
   const [selectedVariant, setSelectedVariant] = useState(0);
   const [showCorrectAnswer, setShowCorrectAnswer] = useState(false);
   const [showWrongAnswer, setShowWrongAnswer] = useState(false);

   const reply = async () => {
      if (question.correct_answer.sequence[0] === selectedVariant) {
         PlayCorrectSound();
         setShowCorrectAnswer(true);
         onSaveResult(true); // Сохранение результата
      } else {
         PlayWrongSound();
         setShowWrongAnswer(true);
         onSaveResult(false); // Сохранение результата
      }
   };

   const handleNext = () => {
      setShowCorrectAnswer(false);
      setShowWrongAnswer(false);
      setSelectedVariant(0);
      onNextQuestion(); // Переход к следующему вопросу
   };

   return (
      <View
         style={{
            height: "100%",
         }}
      >
         <View
            style={{
               display: "flex",
               flexDirection: "column",
               justifyContent: "space-between",
               marginTop: 22,
               height: "80%",
            }}
         >
            <Text
               style={{
                  fontWeight: 700,
                  fontSize: 20,
                  color: "#242B35",
                  width: 300,
                  marginHorizontal: "auto",
               }}
            >
               Выберите перевод
            </Text>

            <View>
               <View
                  style={{
                     display: "flex",
                     flexDirection: "row",
                     alignItems: "center",
                     marginHorizontal: "auto",
                     width: 300,
                     marginBottom: 20,
                  }}
               >
                  <MessageSvg />
                  <Text
                     style={{
                        marginLeft: 7,
                        color: "#3D3D3D",
                        fontWeight: 500,
                        fontSize: 16,
                     }}
                  >
                     {question.correct_answer.kaz_plaintext}
                  </Text>
               </View>

               <View
                  style={{
                     marginHorizontal: "auto",
                     width: 295.48,
                     height: 200,
                     borderRadius: 15,
                     overflow: "hidden",
                  }}
               >
                  <Image
                     style={{
                        width: "100%",
                        height: "100%",
                        resizeMode: "cover", // Изменено с "contain" на "cover"
                     }}
                     source={question.correct_answer.imageUrl}
                  />
               </View>
            </View>

            <View>
               {question.words.map((word: any) => {
                  return (
                     <VariantButton
                        key={word.id}
                        handlePress={() => {
                           if (selectedVariant == word.id) {
                              setSelectedVariant(0);
                              return;
                           }
                           setSelectedVariant(word.id);
                        }}
                        isPicked={selectedVariant == word.id ? true : false}
                        title={word.rus_plaintext}
                     />
                  );
               })}
            </View>

            <ChoiceRequiredButton isValid={selectedVariant !== 0 ? true : false} title="Далее" handlePress={reply} />
         </View>

         {showCorrectAnswer && <LessonAnswerBottom heigth={350} onNext={handleNext} question={question} isCorrect={true} />}
         {showWrongAnswer && <LessonAnswerBottom heigth={350} onNext={handleNext} question={question} isCorrect={false} />}
      </View>
   );
};

const styles = StyleSheet.create({
   modalContainer: {
      flex: 1,
      justifyContent: "center",
      backgroundColor: "rgba(0,0,0,0.5)",
   },
   modalContent: {
      margin: 20,
      backgroundColor: "white",
      borderRadius: 25,
      padding: 35,
      alignItems: "center",
   },
   modalText: {
      marginBottom: 15,
      textAlign: "center",
      fontSize: 16,
      color: "#333",
   },
   modalButtons: {
      flexDirection: "row",
   },
   button: {
      flex: 1,
      marginHorizontal: 5,
      paddingVertical: 10,
      borderRadius: 5,
      backgroundColor: "#F1C644",
      alignItems: "center",
   },
   buttonText: {
      color: "#fff",
      fontWeight: "bold",
   },
});
