import React from "react";
import { TouchableOpacity, Text, StyleSheet, View } from "react-native";

interface ButtonProps {
   title: string;
   isValid: boolean;
   handlePress: () => void;
   color?: string;
   borderColor?: string;
}

const ChoiceRequiredButton: React.FC<ButtonProps> = ({
   title,
   isValid = false,
   color = "#00AFCA",
   borderColor = "#008498",
   handlePress,
}) => {
   return (
      <TouchableOpacity
         disabled={isValid ? false : true}
         onPress={handlePress}
         style={styles.buttonContainer}
      >
         <View
            style={{
               ...styles.container,
               backgroundColor: isValid ? color : "#E2E2E2",
               shadowColor: isValid ? borderColor : "#B3B3B3", // Цвет тени, активная и неактивная
               shadowOffset: { width: 0, height: 4 }, // Смещение тени
               shadowOpacity: 1, // Прозрачность тени
               shadowRadius: 0, // Радиус размытия
               elevation: 4, // Только для Android
            }}
         >
            <Text
               style={{ ...styles.title, color: isValid ? "#fff" : "#B3B3B3" }}
            >
               {title}
            </Text>
         </View>
      </TouchableOpacity>
   );
};

const styles = StyleSheet.create({
   buttonContainer: {
      marginHorizontal: "auto",
   },
   container: {
      borderRadius: 25,
      width: 319,
      height: 56,
      justifyContent: "center",
      alignItems: "center",
   },
   title: {
      fontWeight: "700",
      fontSize: 20,
   },
});

export default ChoiceRequiredButton;
