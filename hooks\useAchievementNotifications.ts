import { useEffect, useRef } from 'react';
import { Alert } from 'react-native';
import * as Notifications from 'expo-notifications';
import { Achievement, UserAchievement } from '../types/module';

// Настройка обработчика уведомлений
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

interface AchievementNotificationData {
  type: 'achievement';
  achievement_id: number;
  achievement_name: string;
  achievement_icon: string;
  reward: number;
}

export const useAchievementNotifications = () => {
  const notificationListener = useRef<Notifications.Subscription>();
  const responseListener = useRef<Notifications.Subscription>();

  useEffect(() => {
    // Слушатель входящих уведомлений
    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      const data = notification.request.content.data as AchievementNotificationData;
      
      if (data.type === 'achievement') {
        // Показать локальное уведомление о достижении
        showAchievementAlert(data);
      }
    });

    // Слушатель ответов на уведомления
    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      const data = response.notification.request.content.data as AchievementNotificationData;
      
      if (data.type === 'achievement') {
        // Можно добавить навигацию к экрану достижений
        console.log('Achievement notification tapped:', data);
      }
    });

    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, []);

  const showAchievementAlert = (data: AchievementNotificationData) => {
    Alert.alert(
      '🎉 Новое достижение!',
      `${data.achievement_icon} ${data.achievement_name}\n+${data.reward} XP`,
      [
        {
          text: 'Отлично!',
          style: 'default',
        },
      ],
      { cancelable: true }
    );
  };

  const scheduleLocalAchievementNotification = async (achievement: Achievement, reward: number) => {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🎉 Новое достижение!',
          body: `${achievement.icon} ${achievement.name}\n+${reward} XP`,
          data: {
            type: 'achievement',
            achievement_id: achievement.id,
            achievement_name: achievement.name,
            achievement_icon: achievement.icon,
            reward: reward,
          } as AchievementNotificationData,
          sound: true,
        },
        trigger: null, // Показать немедленно
      });
    } catch (error) {
      console.error('Error scheduling achievement notification:', error);
    }
  };

  return {
    scheduleLocalAchievementNotification,
  };
};

// Хук для отслеживания изменений в достижениях
export const useAchievementProgress = (
  userAchievements: UserAchievement[],
  onNewAchievement?: (achievement: Achievement, reward: number) => void
) => {
  const previousAchievements = useRef<UserAchievement[]>([]);

  useEffect(() => {
    if (previousAchievements.current.length === 0) {
      previousAchievements.current = userAchievements;
      return;
    }

    // Найти новые достижения
    const newAchievements = userAchievements.filter(current => {
      const previous = previousAchievements.current.find(prev => prev.id === current.id);
      return current.achieved && (!previous || !previous.achieved);
    });

    // Уведомить о новых достижениях
    newAchievements.forEach(userAchievement => {
      if (onNewAchievement) {
        onNewAchievement(userAchievement.achievement, userAchievement.achievement.reward);
      }
    });

    previousAchievements.current = userAchievements;
  }, [userAchievements, onNewAchievement]);
};
