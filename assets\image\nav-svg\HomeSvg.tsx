import { View } from "react-native";
import Svg, { Path, G } from "react-native-svg";

const HomeSvg = ({ color }: { color: string }) => (
   <View style={{ backgroundColor: color, borderRadius: 14, padding: 1, margin: "auto" }}>
      <Svg width="40" height="40" viewBox="0 0 49 50" fill="none">
         <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M43.9601 23.7165L42.3646 22.44V43.4478H44.9166C45.7623 43.4478 46.4479 44.1334 46.4479 44.9791C46.4479 45.8248 45.7623 46.5103 44.9166 46.5103H4.08331C3.23762 46.5103 2.55206 45.8248 2.55206 44.9791C2.55206 44.1334 3.23762 43.4478 4.08331 43.4478H6.63539V22.44L5.03987 23.7165C4.3795 24.2448 3.4159 24.1377 2.8876 23.4773C2.35931 22.8169 2.46637 21.8533 3.12674 21.3251L19.7172 8.05272C22.5134 5.81576 26.4866 5.81576 29.2828 8.05272L45.8732 21.3251C46.5336 21.8533 46.6406 22.8169 46.1123 23.4773C45.5841 24.1377 44.6204 24.2448 43.9601 23.7165ZM24.5 13.8437C21.3991 13.8437 18.8854 16.3574 18.8854 19.4583C18.8854 22.5591 21.3991 25.0728 24.5 25.0728C27.6008 25.0728 30.1146 22.5591 30.1146 19.4583C30.1146 16.3574 27.6008 13.8437 24.5 13.8437ZM28.0644 27.2403C27.1278 27.1144 25.9582 27.1144 24.6008 27.1145H24.3992C23.0417 27.1144 21.8722 27.1144 20.9355 27.2403C19.9318 27.3752 18.9696 27.6796 18.1892 28.46C17.4088 29.2404 17.1045 30.2026 16.9695 31.2063C16.8436 32.1429 16.8436 33.3125 16.8437 34.67L16.8437 43.4478H19.9062H29.0937H32.1562L32.1562 34.67L32.1562 34.4176C32.1558 33.1672 32.1485 32.0844 32.0304 31.2063C31.8955 30.2026 31.5912 29.2404 30.8107 28.46C30.0303 27.6796 29.0682 27.3752 28.0644 27.2403Z"
            fill="#ED8A19"
         />
         <G opacity="0.5">
            <Path
               fill-rule="evenodd"
               clip-rule="evenodd"
               d="M21.948 19.4583C21.948 18.0489 23.0906 16.9062 24.5001 16.9062C25.9096 16.9062 27.0522 18.0489 27.0522 19.4583C27.0522 20.8678 25.9096 22.0104 24.5001 22.0104C23.0906 22.0104 21.948 20.8678 21.948 19.4583Z"
               fill="#F7AD3A"
            />
            <Path
               fill-rule="evenodd"
               clip-rule="evenodd"
               d="M21.948 19.4583C21.948 18.0489 23.0906 16.9062 24.5001 16.9062C25.9096 16.9062 27.0522 18.0489 27.0522 19.4583C27.0522 20.8678 25.9096 22.0104 24.5001 22.0104C23.0906 22.0104 21.948 20.8678 21.948 19.4583Z"
               fill="#F7AD3A"
            />
         </G>
         <Path
            opacity="0.5"
            d="M24.6008 27.1147C25.9582 27.1147 27.1278 27.1146 28.0644 27.2405C29.0682 27.3755 30.0304 27.6798 30.8108 28.4602C31.5912 29.2407 31.8955 30.2028 32.0305 31.2066C32.1485 32.0846 32.1559 33.1675 32.1562 34.4179L32.1563 43.4481H16.8438L16.8438 34.6702C16.8437 33.3128 16.8436 32.1432 16.9695 31.2066C17.1045 30.2028 17.4088 29.2407 18.1892 28.4602C18.9697 27.6798 19.9318 27.3755 20.9356 27.2405C21.8722 27.1146 23.0418 27.1147 24.3992 27.1147H24.6008Z"
            fill="#F7AD3A"
         />
         <Path
            opacity="0.5"
            d="M32.6666 6.1875H37.7708C38.3345 6.1875 38.7916 6.64454 38.7916 7.20833L38.7916 15.6599L31.6458 9.94326V7.20833C31.6458 6.64454 32.1028 6.1875 32.6666 6.1875Z"
            fill="#F7AD3A"
         />
      </Svg>
   </View>
);

export default HomeSvg;
