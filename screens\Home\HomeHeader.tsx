import { Image, Platform, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, Oyu } from "../../assets/image";
import { useNavigation } from "@react-navigation/native";
import { SafeAreaView } from "react-native-safe-area-context";
import { GetRequest } from "../../utils/service";
import { useAuth } from "../../context/AuthContext";

export default function HomeHeader() {
   const { navigate } = useNavigation();
   const { authState } = useAuth();

   const [streak, setStreak] = useState<number>(0);

   useEffect(() => {
      const fetchData = async () => {
         try {
            const response = await GetRequest(`/v1/progress/streak/${authState.user?.id}`);

            setStreak(response.streak.current_streak);
         } catch (error) {
            console.log(error);
         }
      };

      fetchData();
   }, []);

   return (
      <SafeAreaView style={{ ...styles.headerContainer, paddingTop: Platform.OS === "android" ? 10 : 0 }}>
         <View
            style={{
               ...styles.innerContainer,
            }}
         >
            {/* Элементы (в первой строке) */}
            <View style={styles.row}>
               {/* Первый элемент */}
               <View style={{ alignItems: "center" }}>
                  <View style={styles.container}>
                     <Image source={Oyu} style={styles.image} />
                     <View style={styles.textContainer}>
                        <Text style={styles.text}>{streak}</Text>
                     </View>
                  </View>
               </View>

               {/* Второй элемент */}
               <View style={{ alignItems: "center" }}>
                  <View
                     style={{
                        width: 146,
                        height: 35,
                        backgroundColor: "#fff",
                        borderRadius: 15,
                        justifyContent: "center",
                        alignItems: "center",
                     }}
                  >
                     <View
                        style={{
                           display: "flex",
                           flexDirection: "row",
                           alignItems: "center",
                        }}
                     >
                        {/* <Image width={25} height={25} source={OrangePro} /> */}
                        <View style={{ display: "flex", flexDirection: "row" }}>
                           <Text
                              style={{
                                 fontWeight: "700",
                                 fontSize: 10,
                                 color: "#242B35",
                              }}
                           >
                              Основы языка
                           </Text>
                        </View>
                     </View>
                  </View>
               </View>

               {/* Третий элемент */}
               <View style={{ alignItems: "center" }}>
                  <View
                     style={{
                        width: 40,
                        height: 40,
                        borderRadius: 100,
                        backgroundColor: "#fff",
                     }}
                  ></View>
               </View>
            </View>

            {/* Тексты под элементами (вторая строка) */}
            <View style={styles.rowBottomText}>
               <Text style={styles.labelText}>Темп</Text>
               <Text style={styles.labelText}>Начальный уровень - Курс 1</Text>
               <Text style={styles.labelText}>Озвучка</Text>
            </View>
         </View>
      </SafeAreaView>
   );
}

const styles = StyleSheet.create({
   container: {
      width: 50,
      height: 50,
      position: "relative",
   },
   image: {
      width: 50,
      height: 50,
   },
   textContainer: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: "center",
      alignItems: "center",
   },
   text: {
      color: "#ED8A19",
      fontWeight: "500",
      fontSize: 20,
   },
   labelText: {
      fontSize: 12,
      fontWeight: "bold",
      color: "#fff",
      textAlign: "center",
   },
   headerContainer: {
      backgroundColor: "#019AB2",
      paddingVertical: 0,
      paddingBottom: 15,
   },
   innerContainer: {
      width: "90%",
      marginHorizontal: "auto",
      display: "flex",
      flexDirection: "column",
      justifyContent: "center",
      alignItems: "center",
   },
   row: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      width: "100%",
      alignItems: "center",
   },
   rowBottomText: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      width: "100%",
      marginLeft: 16,
      marginTop: 10,
   },
});
