import { Platform, Text, TouchableOpacity, View } from "react-native";
import React from "react";
import BackArrowSvg from "../../../assets/svg/BackArrowSvg";
import HistoryHeaderButton from "./HistoryHeaderButton";

import HistoryBookSvg from "../../../assets/svg/HistortBookSvg";
import { useNavigation } from "@react-navigation/native";

export default function HistoryHeader() {
   const { navigate } = useNavigation();

   return (
      <View
         style={{
            marginTop: Platform.OS === "android" ? 50 : 0,
            borderBottomWidth: 1,
            borderBottomColor: "#E1E1E1",
         }}
      >
         <TouchableOpacity
            onPress={() => {
               // @ts-ignore
               navigate("Practice");
            }}
            style={{ width: 30, height: 30, marginLeft: 10 }}
         >
            <View style={{ margin: "auto" }}>
               <BackArrowSvg />
            </View>
         </TouchableOpacity>

         <View
            style={{
               display: "flex",
               flexDirection: "row",
               justifyContent: "space-between",
               width: "90%",
               marginHorizontal: "auto",
               alignItems: "center",
               height: 130,
            }}
         >
            <View
               style={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "space-between",
                  paddingVertical: 15,
                  //   marginHorizontal: 11,
                  height: "100%",
               }}
            >
               <View>
                  <Text
                     style={{
                        fontWeight: 600,
                        fontSize: 20,
                        color: "#242B35",
                     }}
                  >
                     Повторение слов
                  </Text>
                  <Text
                     style={{
                        fontWeight: 400,
                        fontSize: 12,
                        color: "#242B35",
                        marginTop: 10,
                     }}
                  >
                     Самый эффективный способ запоминания
                  </Text>
               </View>

               <HistoryHeaderButton title="Начать +15 XP" handlePress={() => {}} />
            </View>

            <View>
               <HistoryBookSvg />
            </View>
         </View>
      </View>
   );
}
