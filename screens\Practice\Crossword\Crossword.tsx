import React, { useState } from "react";
import { StyleSheet, Text, View, TouchableOpacity } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import RightSvg from "../../../assets/svg/RightSvg";
import LeftSvg from "../../../assets/svg/LeftSvg";
import ChoiceRequiredButton from "../../../components/ui/ChoiceRequiredButton";
import { useNavigation } from "@react-navigation/native";
import XSvg from "../../../assets/svg/xSvg";

export default function Crossword() {
   const { navigate } = useNavigation();

   const crosswordLayout = [
      ["м", " ", " ", " ", " ", " "],
      ["-", "-", "-", "-", " ", "-"],
      ["-", "-", "-", "-", " ", "-"],
      ["-", "-", "-", " ", "п", " "],
   ];

   const correctAnswers = [
      ["м", "е", "к", "т", "е", "п"],
      ["-", "-", "-", "-", "с", "-"],
      ["-", "-", "-", "-", "е", "-"],
      ["-", "-", "-", "а", "п", "а"],
   ];

   const hints = ["Білім ортасы", "Математикалық тапсырма", "Жақын туыс"];

   const row1 = [
      [0, 0],
      [0, 1],
      [0, 2],
      [0, 3],
      [0, 4],
      [0, 5],
   ];
   const row2 = [
      [0, 4],
      [1, 4],
      [2, 4],
      [3, 4],
   ];
   const row3 = [
      [3, 3],
      [3, 4],
      [3, 5],
   ];
   const rows = [row1, row2, row3];

   const [crosswordState, setCrosswordState] = useState(crosswordLayout);
   const [inputText, setInputText] = useState("");
   const [currentRowIndex, setCurrentRowIndex] = useState(0);
   const [isCorrect, setIsCorrect] = useState(null);

   const handleKeyPress = (key: any) => {
      const currentRow = getCurrentRow();
      const maxLength = currentRow.filter(([row, col]) => crosswordLayout[row][col] === " ").length;

      if (key === "[<-]") {
         setInputText((prevText) => {
            const newText = prevText.slice(0, -1);
            updateDeleteLetter();
            return newText;
         });
      } else {
         setInputText((prevText) => {
            if (prevText.length < maxLength) {
               updateCrossword(key);
               return prevText + key;
            }
            return prevText;
         });
      }
   };

   const updateCrossword = (letter: any) => {
      const currentRow = getCurrentRow();
      let inputIndex = 0;

      const updatedCrossword = crosswordState.map((row, rowIndex) => {
         return row.map((cell, colIndex) => {
            const isInCurrentRow = currentRow.some(([r, c]) => r === rowIndex && c === colIndex);

            if (isInCurrentRow && cell === " " && inputIndex === 0) {
               inputIndex++;
               return letter;
            }

            return cell;
         });
      });

      setCrosswordState(updatedCrossword);
   };

   const updateDeleteLetter = () => {
      const currentRow = getCurrentRow();
      let lastFilledIndex = -1;

      const updatedCrossword = crosswordState.map((row, rowIndex) => {
         return row.map((cell, colIndex) => {
            const isInCurrentRow = currentRow.some(([r, c]) => r === rowIndex && c === colIndex);

            if (isInCurrentRow && cell !== " " && crosswordLayout[rowIndex][colIndex] === " ") {
               // @ts-ignore
               lastFilledIndex = [rowIndex, colIndex];
            }

            return cell;
         });
      });

      if (lastFilledIndex !== -1) {
         // @ts-ignore
         const [lastRow, lastCol] = lastFilledIndex;
         updatedCrossword[lastRow][lastCol] = " ";
      }

      setCrosswordState(updatedCrossword);
   };

   const getCurrentRow = () => {
      return rows[currentRowIndex];
   };

   const checkAnswer = () => {
      const currentRow = getCurrentRow();
      const userAnswer = currentRow.map(([row, col], index) =>
         crosswordState[row][col] === " " ? inputText[index] || "" : crosswordState[row][col]
      );

      const correctAnswer = currentRow.map(([row, col]) => correctAnswers[row][col]);

      const isRowComplete = userAnswer.filter((char) => char !== "").length === currentRow.length;

      if (isRowComplete) {
         const isCorrect = userAnswer.join("") === correctAnswer.join("");

         // @ts-ignore
         setIsCorrect(isCorrect);
         // handleNext();
      } else {
         setIsCorrect(null);
      }
   };

   React.useEffect(() => {
      checkAnswer();
   }, [crosswordState]);

   // @ts-ignore
   const renderCell = (item, rowIndex, colIndex) => {
      const isFilled = item !== " ";
      const isEmpty = item === "-";
      const isInCurrentRow = getCurrentRow().some(([r, c]) => r === rowIndex && c === colIndex);

      if (isEmpty) {
         return <View key={`${rowIndex}-${colIndex}`} style={styles.noCell} />;
      }

      return (
         <View
            key={`${rowIndex}-${colIndex}`}
            style={[
               styles.cell,
               isFilled ? styles.filledCell : styles.emptyCell,
               isInCurrentRow &&
                  (isCorrect === null ? styles.selectedTarget : isCorrect ? styles.correctTarget : styles.incorrectTarget),
            ]}
         >
            <Text style={styles.cellText}>{item}</Text>
         </View>
      );
   };

   const handlePrev = () => {
      setInputText("");
      setIsCorrect(null);
      setCurrentRowIndex((prev) => (prev > 0 ? prev - 1 : rows.length - 1));
   };

   const handleNext = () => {
      setInputText("");
      setIsCorrect(null);
      setCurrentRowIndex((prev) => (prev < rows.length - 1 ? prev + 1 : 0));
   };

   const isCrosswordCorrect = () => {
      for (let i = 0; i < crosswordState.length; i++) {
         for (let j = 0; j < crosswordState[i].length; j++) {
            if (crosswordState[i][j] !== correctAnswers[i][j]) {
               return false; // Если хотя бы одна ячейка не совпадает, возвращаем false
            }
         }
      }
      return true; // Если все ячейки совпадают, возвращаем true
   };

   return (
      <LinearGradient style={{ flex: 1 }} colors={["#D2E8FF", "#00AFCA"]}>
         <TouchableOpacity
            onPress={() => {
               //@ts-ignore
               navigate("Practice");
            }}
            style={{
               width: 32,
               height: 32,
               marginTop: 50,
               marginLeft: 16,
               display: "flex",
               justifyContent: "center",
            }}
         >
            <View style={{ marginHorizontal: "auto" }}>
               <XSvg />
            </View>
         </TouchableOpacity>

         <View style={styles.container}>
            <View style={styles.crosswordGrid}>
               {crosswordState.map((row, rowIndex) => (
                  <View key={`row-${rowIndex}`} style={styles.row}>
                     {row.map((item, colIndex) => renderCell(item, rowIndex, colIndex))}
                  </View>
               ))}
            </View>

            <View
               style={{
                  width: "100%",
                  marginHorizontal: "auto",
                  backgroundColor: "#019AB2",
                  display: "flex",
                  flexDirection: "row",
                  paddingVertical: 25,
                  justifyContent: "center",
                  alignItems: "center",
               }}
            >
               <TouchableOpacity onPress={handlePrev}>
                  <LeftSvg />
               </TouchableOpacity>
               <Text style={styles.hintText}>{hints[currentRowIndex]}</Text>
               <TouchableOpacity onPress={handleNext}>
                  <RightSvg />
               </TouchableOpacity>
            </View>

            <View style={styles.keyboardContainer}>
               {keyLayout.map((row, rowIndex) => (
                  <View key={`row-${rowIndex}`} style={styles.keyRow}>
                     {row.map((key) => (
                        <TouchableOpacity key={key} style={styles.keyButton} onPress={() => handleKeyPress(key)}>
                           <Text style={styles.keyText}>{key}</Text>
                        </TouchableOpacity>
                     ))}
                  </View>
               ))}
            </View>

            <ChoiceRequiredButton
               title="Готово"
               handlePress={() => {
                  // @ts-ignore
                  navigate("CrosswordCorrectPage");
               }}
               isValid={isCrosswordCorrect()}
            />
         </View>
      </LinearGradient>
   );
}

const styles = StyleSheet.create({
   container: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
   },
   crosswordGrid: {
      marginVertical: 20,
   },
   row: {
      flexDirection: "row",
   },
   cell: {
      width: 50,
      height: 50,
      borderWidth: 2,
      justifyContent: "center",
      alignItems: "center",
      margin: 1,
   },
   noCell: {
      opacity: 0,
      width: 50,
      height: 50,
      borderWidth: 2,
      justifyContent: "center",
      alignItems: "center",
      margin: 1,
   },
   filledCell: {
      backgroundColor: "#FFC107", // Желтая клетка для предзаполненных букв
      borderColor: "#FFC107",
      borderRadius: 12,
   },
   emptyCell: {
      backgroundColor: "#fff",
      borderColor: "#00afca",
      borderRadius: 12,
   },
   cellText: {
      fontSize: 24,
      color: "#242B35",
      fontWeight: "bold",
   },
   selectedTarget: {
      backgroundColor: "#fdf276", // Подсветка выбранной строки (жёлтый)
      borderColor: "#f7ad3a",
      borderRadius: 12,
   },
   correctTarget: {
      backgroundColor: "#32d583", // Зелёная строка (если правильная)
      borderColor: "#027a48",
      borderRadius: 12,
   },
   incorrectTarget: {
      backgroundColor: "#fd7676", // Красная строка (если неправильная)
      borderColor: "#ed2b08",
      borderRadius: 12,
   },
   controls: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 10,

      backgroundColor: "#000",
   },
   hintText: {
      fontSize: 20,
      fontWeight: "bold",
      color: "#fff",
      marginHorizontal: 20,
   },
   arrow: {
      fontSize: 24,
      fontWeight: "bold",
      color: "#007BFF",
   },
   keyboardContainer: {
      marginTop: 20,
      paddingHorizontal: 10,

      borderRadius: 16,
      paddingTop: 8,
      paddingBottom: 23,
   },
   keyRow: {
      flexDirection: "row",
      justifyContent: "center",
      marginBottom: 8,
   },
   keyButton: {
      paddingVertical: 12,
      paddingHorizontal: 8,
      marginHorizontal: 2,
      backgroundColor: "#eee",
      alignItems: "center",
      borderRadius: 4,
      minWidth: 24,
   },
   keyText: {
      fontSize: 16,
      color: "#242B35",
   },
});

const keyLayout = [
   ["ә", "і", "ң", "ғ", "ұ", "ү", "қ", "ө", "һ"],
   ["й", "ц", "у", "к", "е", "н", "г", "ш", "щ", "з", "х", "ъ"],
   ["ф", "ы", "в", "а", "п", "р", "о", "л", "д", "ж", "э"],
   ["я", "ч", "с", "м", "и", "т", "ь", "б", "ю", "[<-]"],
];
