# API Документация - Система Достижений (Админ Панель)

## Обзор

Система достижений позволяет администраторам создавать, управлять и отслеживать достижения пользователей в приложении изучения казахского языка.

## Аутентификация

Все админские эндпоинты требуют:
- Валидный JWT токен в заголовке `Authorization: Bearer <token>`
- Роль пользователя должна быть `admin`

## Базовый URL

```
https://your-api-domain.com/v1/admin/achievements
```

## Эндпоинты

### 1. Создание достижения

**POST** `/v1/admin/achievements/create`

Создает новое достижение в системе.

#### Тело запроса:
```json
{
  "name": "Первые шаги",
  "description": "Пройдите первый урок",
  "type": "lessons",
  "target": 1,
  "icon": "🎯",
  "reward": 10,
  "category": "progress",
  "difficulty": "easy",
  "is_active": true,
  "metadata": {
    "milestone": "first_lesson"
  }
}
```

#### Параметры:
- `name` (string, обязательно) - Название достижения
- `description` (string, обязательно) - Описание достижения
- `type` (string, обязательно) - Тип достижения (см. [Типы достижений](#типы-достижений))
- `target` (integer, обязательно) - Целевое значение для получения достижения
- `icon` (string, опционально) - Иконка достижения (emoji или URL)
- `reward` (integer, опционально) - Награда в очках (по умолчанию: 0)
- `category` (string, опционально) - Категория (по умолчанию: "general")
- `difficulty` (string, опционально) - Сложность (по умолчанию: "easy")
- `is_active` (boolean, опционально) - Активно ли достижение (по умолчанию: true)
- `metadata` (object, опционально) - Дополнительные параметры

#### Ответ:
```json
{
  "achievement": {
    "id": 1,
    "name": "Первые шаги",
    "description": "Пройдите первый урок",
    "type": "lessons",
    "target": 1,
    "icon": "🎯",
    "reward": 10,
    "category": "progress",
    "difficulty": "easy",
    "is_active": true,
    "metadata": {
      "milestone": "first_lesson"
    },
    "created_at": "2025-08-05T00:00:00Z",
    "updated_at": "2025-08-05T00:00:00Z"
  }
}
```

### 2. Получение списка всех достижений

**GET** `/v1/admin/achievements/list`

Возвращает все достижения, включая неактивные.

#### Ответ:
```json
{
  "achievements": [
    {
      "id": 1,
      "name": "Первые шаги",
      "description": "Пройдите первый урок",
      "type": "lessons",
      "target": 1,
      "icon": "🎯",
      "reward": 10,
      "category": "progress",
      "difficulty": "easy",
      "is_active": true,
      "metadata": {},
      "created_at": "2025-08-05T00:00:00Z",
      "updated_at": "2025-08-05T00:00:00Z"
    }
  ]
}
```

### 3. Получение достижения по ID

**GET** `/v1/admin/achievements/get/{id}`

Возвращает конкретное достижение по его ID.

#### Параметры URL:
- `id` (integer) - ID достижения

#### Ответ:
```json
{
  "achievement": {
    "id": 1,
    "name": "Первые шаги",
    "description": "Пройдите первый урок",
    "type": "lessons",
    "target": 1,
    "icon": "🎯",
    "reward": 10,
    "category": "progress",
    "difficulty": "easy",
    "is_active": true,
    "metadata": {},
    "created_at": "2025-08-05T00:00:00Z",
    "updated_at": "2025-08-05T00:00:00Z"
  }
}
```

### 4. Обновление достижения

**PUT** `/v1/admin/achievements/update/{id}`

Обновляет существующее достижение. Можно передавать только те поля, которые нужно изменить.

#### Параметры URL:
- `id` (integer) - ID достижения

#### Тело запроса:
```json
{
  "name": "Новое название",
  "reward": 20,
  "is_active": false
}
```

#### Ответ:
```json
{
  "achievement": {
    "id": 1,
    "name": "Новое название",
    "description": "Пройдите первый урок",
    "type": "lessons",
    "target": 1,
    "icon": "🎯",
    "reward": 20,
    "category": "progress",
    "difficulty": "easy",
    "is_active": false,
    "metadata": {},
    "created_at": "2025-08-05T00:00:00Z",
    "updated_at": "2025-08-05T00:00:00Z"
  }
}
```

### 5. Удаление достижения

**DELETE** `/v1/admin/achievements/delete/{id}`

Деактивирует достижение (мягкое удаление).

#### Параметры URL:
- `id` (integer) - ID достижения

#### Ответ:
```json
{
  "message": "achievement deleted successfully"
}
```

### 6. Получение достижений по категории

**GET** `/v1/admin/achievements/by-category?category={category}`

Возвращает все активные достижения определенной категории.

#### Параметры запроса:
- `category` (string) - Категория достижений

#### Ответ:
```json
{
  "achievements": [
    {
      "id": 1,
      "name": "Первые шаги",
      "category": "progress",
      // ... остальные поля
    }
  ]
}
```

### 7. Получение информации для валидации

**GET** `/v1/admin/achievements/validation-info`

Возвращает списки валидных значений для создания достижений.

#### Ответ:
```json
{
  "types": [
    "lessons",
    "words", 
    "streak",
    "modules",
    "questions",
    "accuracy",
    "speed",
    "consecutive"
  ],
  "categories": [
    "progress",
    "vocabulary",
    "consistency", 
    "accuracy",
    "speed",
    "general"
  ],
  "difficulties": [
    "easy",
    "medium",
    "hard"
  ]
}
```

## Типы достижений

| Тип | Описание | Пример |
|-----|----------|--------|
| `lessons` | Количество пройденных уроков | Пройти 5 уроков |
| `words` | Количество изученных слов | Выучить 10 слов |
| `streak` | Дни подряд занятий | 7 дней подряд |
| `modules` | Количество завершенных модулей | Завершить 3 модуля |
| `questions` | Количество отвеченных вопросов | Ответить на 50 вопросов |
| `accuracy` | Точность ответов | 90% правильных ответов |
| `speed` | Скорость прохождения | Урок за 2 минуты |
| `consecutive` | Подряд правильных ответов | 10 правильных подряд |

## Категории достижений

| Категория | Описание |
|-----------|----------|
| `progress` | Прогресс в обучении |
| `vocabulary` | Словарный запас |
| `consistency` | Постоянство занятий |
| `accuracy` | Точность ответов |
| `speed` | Скорость обучения |
| `general` | Общие достижения |

## Уровни сложности

| Сложность | Описание |
|-----------|----------|
| `easy` | Легкие достижения |
| `medium` | Средние достижения |
| `hard` | Сложные достижения |

## Коды ошибок

| Код | Описание |
|-----|----------|
| 400 | Неверные данные запроса |
| 401 | Не авторизован |
| 403 | Нет прав доступа |
| 404 | Достижение не найдено |
| 500 | Внутренняя ошибка сервера |

## Примеры использования

### Создание достижения за словарный запас:
```bash
curl -X POST "https://api.example.com/v1/admin/achievements/create" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Полиглот",
    "description": "Выучите 10 новых слов",
    "type": "words",
    "target": 10,
    "icon": "🗣️",
    "reward": 30,
    "category": "vocabulary",
    "difficulty": "easy"
  }'
```

### Получение всех достижений категории "progress":
```bash
curl -X GET "https://api.example.com/v1/admin/achievements/by-category?category=progress" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
