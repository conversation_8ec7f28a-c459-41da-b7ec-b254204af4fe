import { View } from "react-native";
import Svg, { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Defs, Rect } from "react-native-svg";

const PracticeSvg = ({ color }: { color: string }) => (
   <View style={{ backgroundColor: color, borderRadius: 14, padding: 1 }}>
      <Svg width="40" height="40" viewBox="0 0 50 50" fill="none">
         <G clip-path="url(#clip0_515_1370)">
            <G clip-path="url(#clip1_515_1370)">
               <Path
                  d="M22.0689 38.9651L18.4963 41.0277C17.8545 41.3983 17.0262 41.1763 16.6557 40.5345L6.16053 22.3564C5.78998 21.7145 6.01191 20.8863 6.65372 20.5157L10.2263 18.4531C10.8682 18.0825 11.6964 18.3045 12.067 18.9463L22.5621 37.1244C22.9328 37.7662 22.7107 38.5945 22.0689 38.9651Z"
                  fill="#77757E"
               />
               <Path
                  d="M14.749 39.9269L12.5864 41.1755C11.9445 41.546 11.1163 41.3241 10.7457 40.6823L3.07752 27.4005C2.70697 26.7587 2.9289 25.9305 3.57071 25.5599L5.73331 24.3114C6.37513 23.9408 7.20338 24.1627 7.57393 24.8045L15.2422 38.0863C15.6127 38.7281 15.3908 39.5564 14.749 39.9269Z"
                  fill="#ACAAB1"
               />
               <Path d="M5.40528 31.4316L1.54102 33.6626L4.55407 38.8814L8.41834 36.6503L5.40528 31.4316Z" fill="#3E3B43" />
               <Path
                  d="M28.2299 8.05847L31.8025 5.99581C32.4443 5.62526 33.2726 5.84719 33.6431 6.489L44.1383 24.6672C44.5088 25.309 44.2869 26.1372 43.6451 26.5078L40.0725 28.5704C39.4307 28.941 38.6024 28.7191 38.2319 28.0772L27.7367 9.89908C27.3661 9.25727 27.5881 8.42902 28.2299 8.05847Z"
                  fill="#77757E"
               />
               <Path
                  d="M35.5495 7.09655L37.7121 5.84797C38.3539 5.47742 39.1821 5.69935 39.5527 6.34116L47.2209 19.6229C47.5915 20.2647 47.3695 21.093 46.7277 21.4635L44.5651 22.7121C43.9233 23.0827 43.0951 22.8607 42.7245 22.2189L35.0563 8.93717C34.6857 8.29535 34.9077 7.4671 35.5495 7.09655Z"
                  fill="#ACAAB1"
               />
               <Path d="M45.7441 8.14201L41.8799 10.373L44.8929 15.5918L48.7572 13.3608L45.7441 8.14201Z" fill="#3E3B43" />
               <Path d="M31.4776 16.3788L15.8079 25.4258L18.8209 30.6446L34.4907 21.5976L31.4776 16.3788Z" fill="#3E3B43" />
               <Path d="M7.28394 34.6855L3.41968 36.9165L4.55426 38.8817L8.41852 36.6506L7.28394 34.6855Z" fill="#3E3B43" />
               <Path d="M47.6228 11.3957L43.7585 13.6267L44.8931 15.5919L48.7574 13.3608L47.6228 11.3957Z" fill="#3E3B43" />
               <Path d="M33.3563 19.6325L17.6865 28.6794L18.8211 30.6446L34.4909 21.5976L33.3563 19.6325Z" fill="#3E3B43" />
               <Path
                  d="M22.0686 38.9649L18.4959 41.0276C17.8541 41.3982 17.0259 41.1762 16.6553 40.5344L14.2025 36.286C13.832 35.6442 14.0539 34.816 14.6957 34.4454L18.2683 32.3828C18.9101 32.0122 19.7384 32.2342 20.1089 32.876L22.5617 37.1243C22.9324 37.7661 22.7104 38.5944 22.0686 38.9649Z"
                  fill="#77757E"
               />
               <Path
                  d="M43.6796 26.5677L40.107 28.6304C39.4652 29.0009 38.637 28.779 38.2664 28.1372L35.8136 23.8888C35.4431 23.247 35.665 22.4188 36.3068 22.0482L39.8794 19.9856C40.5212 19.615 41.3495 19.8369 41.72 20.4787L44.1728 24.7271C44.5434 25.3689 44.3214 26.1972 43.6796 26.5677Z"
                  fill="#77757E"
               />
               <Path
                  opacity="0.43"
                  d="M41.508 17.4164L43.6706 16.1678C44.3124 15.7972 45.1406 16.0192 45.5112 16.661L47.2214 19.6231C47.5919 20.265 47.37 21.0932 46.7282 21.4638L44.5656 22.7123C43.9238 23.0829 43.0955 22.861 42.725 22.2191L41.0148 19.257C40.6442 18.6152 40.8662 17.7869 41.508 17.4164Z"
                  fill="#ACAAB1"
               />
               <Path
                  opacity="0.43"
                  d="M9.52872 35.8795L11.6913 34.6309C12.3331 34.2604 13.1614 34.4823 13.5319 35.1241L15.2421 38.0863C15.6127 38.7281 15.3908 39.5563 14.7489 39.9269L12.5863 41.1755C11.9445 41.546 11.1163 41.3241 10.7457 40.6823L9.03553 37.7201C8.66498 37.0783 8.88691 36.2501 9.52872 35.8795Z"
                  fill="#ACAAB1"
               />
            </G>
         </G>
         <Defs>
            <ClipPath id="clip0_515_1370">
               <Rect width="49" height="49" fill="white" transform="translate(0.666748 0.0625)" />
            </ClipPath>
            <ClipPath id="clip1_515_1370">
               <Rect width="51.0417" height="51.0417" fill="white" transform="translate(-9.69556 14.2004) rotate(-30)" />
            </ClipPath>
         </Defs>
      </Svg>
   </View>
);

export default PracticeSvg;
