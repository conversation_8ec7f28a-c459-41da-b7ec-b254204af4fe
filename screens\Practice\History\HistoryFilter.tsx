import { StyleSheet, Text, View } from "react-native";
import React from "react";
import FilterSvg from "../../../assets/svg/FilterSvg";

export default function HistoryFilter() {
   return (
      <View
         style={{
            marginTop: 10,
            width: "100%",
            height: 45,
         }}
      >
         <View
            style={{
               width: "90%",
               marginHorizontal: "auto",
               display: "flex",
               flexDirection: "row",
               justifyContent: "space-between",
               alignItems: "center",
               marginVertical: "auto",
            }}
         >
            <Text style={{ color: "#242B35", fontWeight: 700, fontSize: 16 }}>
               5 слов
            </Text>

            <View
               style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
               }}
            >
               <Text
                  style={{
                     color: "#242B35",
                     fontWeight: 500,
                     fontSize: 12,
                     marginRight: 5,
                  }}
               >
                  Недавно добавленные
               </Text>

               <FilterSvg />
            </View>
         </View>
      </View>
   );
}
