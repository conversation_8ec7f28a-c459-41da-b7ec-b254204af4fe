import React, { useState } from "react";
import {
   StyleSheet,
   Text,
   TextInput,
   View,
   TouchableOpacity,
   KeyboardAvoidingView,
   Platform,
   ScrollView,
   Image,
} from "react-native";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";
import { useNavigation } from "@react-navigation/native";
import { baseUrl, PostRequest } from "../../utils/service";
import { User } from "../../types/auth";
import { useAuth } from "../../context/AuthContext";
import axios from "axios";
import GoogleIcon from "../../assets/svg/GoogleIcon";
import { Apple, Facebook } from "../../assets/image";

export default function AuthScreen() {
   const navigation = useNavigation();
   const [isLogin, setIsLogin] = useState(true);
   const [name, setName] = useState<string>("");
   const [surname, setSurname] = useState<string>("");
   const [email, setEmail] = useState<string>("");
   const [password, setPassword] = useState<string>("");
   const [confirmPassword, setConfirmPassword] = useState<string>("");
   const [error, setError] = useState<string>("");

   const { login } = useAuth();

   const authResponseParser = (response: any) => {
      const u = response.response.user;
      const accessToken = response.response.tokens.access_token;
      const refreshToken = response.response.tokens.refresh_token;

      const user: User = {
         id: u.id,
         name: u.name,
         surname: u.surname,
         email: u.email,
         activated: u.activated,
         role: u.Role,
         image_url: u.image_url,
         created_at: u.created_at,
      };

      return { accessToken, refreshToken, user };
   };

   const onLogin = async () => {
      try {
         const response = await axios.post(`${baseUrl}/v1/auth/login`, {
            email,
            password,
         });

         const { accessToken, refreshToken, user } = authResponseParser(response.data);

         await login(accessToken, refreshToken, user);
      } catch (error: any) {
         if (error.response.status == 401) {
            setError("Неверный логин или пароль");
         } else {
            setError("Ошибка сети");
         }

         console.log(error);
      }
   };

   const onRegister = async () => {
      try {
         const response = await PostRequest("/v1/auth/register", {
            name,
            surname,
            email,
            password,
         });

         const { accessToken, refreshToken, user } = authResponseParser(response);

         await login(accessToken, refreshToken, user);
      } catch (error) {
         console.log(error);
      }
   };

   const handleAuthClick = async () => {
      if (isLogin) {
         await onLogin();
      } else {
         if (password != confirmPassword) {
            setError("Пароли не совпадают!");
         }
         await onRegister();
      }
   };

   return (
      <SafeAreaProvider>
         <SafeAreaView style={styles.container}>
            <KeyboardAvoidingView behavior={Platform.OS === "ios" ? "padding" : "height"} style={styles.container}>
               <ScrollView contentContainerStyle={styles.scrollContent}>
                  {/* <TouchableOpacity
                     style={styles.backButton}
                     onPress={() => navigation.goBack()} // Возврат на предыдущий экран
                  >
                     <Text style={styles.backButtonText}>Назад</Text>
                  </TouchableOpacity> */}

                  <View style={styles.formContainer}>
                     <Text style={styles.headerText}>{isLogin ? "Войти" : "Регистрация"}</Text>

                     {!isLogin && (
                        <TextInput
                           defaultValue={name}
                           onChangeText={(name) => setName(name)}
                           placeholder="Имя"
                           style={styles.input}
                        />
                     )}

                     {!isLogin && (
                        <TextInput
                           defaultValue={surname}
                           onChangeText={(surname) => setSurname(surname)}
                           placeholder="Фамилия"
                           style={styles.input}
                        />
                     )}

                     {/* Email Field */}
                     <TextInput
                        defaultValue={email}
                        onChangeText={(email) => setEmail(email)}
                        placeholder="Email"
                        style={styles.input}
                        keyboardType="email-address"
                     />

                     {/* Password Field */}
                     <TextInput
                        defaultValue={password}
                        onChangeText={(password) => setPassword(password)}
                        placeholder="Пароль"
                        style={styles.input}
                        secureTextEntry
                     />

                     {/* Confirm Password Field (only for Registration) */}
                     {!isLogin && (
                        <TextInput
                           defaultValue={confirmPassword}
                           onChangeText={(confirmPassword) => setConfirmPassword(confirmPassword)}
                           placeholder="Повторите пароль"
                           style={styles.input}
                           secureTextEntry
                        />
                     )}

                     <Text style={{ color: "red", marginBottom: 8 }}>{error}</Text>

                     {isLogin && (
                        // @ts-ignore
                        <TouchableOpacity onPress={() => navigation.navigate("ForgotPasswordScreen")}>
                           <Text style={styles.forgotPassword}>Забыли пароль?</Text>
                        </TouchableOpacity>
                     )}

                     <TouchableOpacity onPress={handleAuthClick} style={styles.button}>
                        <Text style={styles.buttonText}>{isLogin ? "Войти" : "Зарегистрироваться"}</Text>
                     </TouchableOpacity>

                     <View style={styles.separator}>
                        <View style={styles.line} />
                        <Text style={styles.separatorText}>или</Text>
                        <View style={styles.line} />
                     </View>

                     <TouchableOpacity style={styles.authButton}>
                        <View style={{ margin: "auto", flexDirection: "row" }}>
                           <GoogleIcon />
                           <Text style={styles.authButtonText}>Войти с помощью Google</Text>
                        </View>
                     </TouchableOpacity>

                     <TouchableOpacity style={styles.authButton}>
                        <View style={{ margin: "auto", flexDirection: "row" }}>
                           <Image style={{ width: 16, height: 16 }} source={Apple} />
                           <Text style={styles.authButtonText}>Войти с помощью Apple</Text>
                        </View>
                     </TouchableOpacity>

                     <TouchableOpacity style={styles.authButton}>
                        <View style={{ margin: "auto", flexDirection: "row" }}>
                           <Image style={{ width: 16, height: 16 }} source={Facebook} />
                           <Text style={styles.authButtonText}>Войти с помощью Facebook</Text>
                        </View>
                     </TouchableOpacity>

                     <TouchableOpacity onPress={() => setIsLogin(!isLogin)}>
                        <Text style={styles.toggleText}>
                           {isLogin ? "Нет аккаунта? Зарегистрироваться" : "Уже есть аккаунт? Войти"}
                        </Text>
                     </TouchableOpacity>
                  </View>
               </ScrollView>
            </KeyboardAvoidingView>
         </SafeAreaView>
      </SafeAreaProvider>
   );
}

const styles = StyleSheet.create({
   container: {
      flex: 1,
      backgroundColor: "#fff",
   },
   scrollContent: {
      flexGrow: 1,
      justifyContent: "center",
      alignItems: "center",
   },
   backButton: {
      position: "absolute",
      top: 10,
      left: 15,
      zIndex: 1,
      padding: 10,
   },
   backButtonText: {
      fontSize: 12,
      color: "#007BFF",
   },
   formContainer: {
      width: "85%",
      backgroundColor: "#fff",
      padding: 20,
      borderRadius: 10,

      shadowOffset: { width: 0, height: 2 },
   },
   headerText: {
      fontSize: 20,
      fontWeight: "bold",
      textAlign: "center",
      marginBottom: 20,
   },
   input: {
      width: "100%",
      height: 50,
      borderWidth: 1,
      borderColor: "#ccc",
      borderRadius: 8,
      paddingHorizontal: 10,
      marginBottom: 15,
      fontSize: 14,
      fontWeight: "500",
   },
   button: {
      backgroundColor: "#018DCB",
      height: 50,
      borderRadius: 8,
      justifyContent: "center",
      alignItems: "center",
      marginBottom: 15,
   },
   buttonText: {
      color: "#fff",
      fontSize: 18,
      fontWeight: "bold",
   },
   forgotPassword: {
      textAlign: "center",
      color: "#007BFF",
      fontSize: 12,
      marginBottom: 10,
   },
   toggleText: {
      textAlign: "center",
      color: "#007BFF",
      fontSize: 12,
      marginTop: 10,
   },
   separator: {
      flexDirection: "row",
      alignItems: "center",
      marginVertical: 15,
   },
   line: {
      flex: 1,
      height: 1,
      backgroundColor: "#ccc",
   },
   separatorText: {
      marginHorizontal: 10,
      fontSize: 14,
      color: "#999",
   },
   authButton: {
      width: "100%",
      margin: "auto",
      height: 35,
      borderColor: "#ccc",
      borderWidth: 1,
      borderRadius: 8,
      paddingHorizontal: 10,
      marginBottom: 8,
      flexDirection: "row",
      alignItems: "center",
   },
   authButtonText: { fontWeight: 500, marginLeft: 8 },
});
