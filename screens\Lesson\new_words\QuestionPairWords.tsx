import { useEffect, useState } from "react";
import { PlayCorrectSound, PlayWrongSound } from "../../../utils/PlaySound";
import { Image, Text, View } from "react-native";
import ChoiceRequiredButton from "../../../components/ui/ChoiceRequiredButton";
import LessonAnswerBottom from "../LessonAnswerBottom";
import PairWordButton from "../../../components/ui/PairWordButton";

const QuestionPairWords = ({
   question,
   onNextQuestion,
   onSaveResult,
}: {
   question: any;
   onNextQuestion: () => void;
   onSaveResult: (isCorrect: boolean) => void;
}) => {
   const [showCorrectAnswer, setShowCorrectAnswer] = useState(false);
   const [showWrongAnswer, setShowWrongAnswer] = useState(false);

   const handleNext = () => {
      onSaveResult(true); // Сохранение результата
      setShowCorrectAnswer(false);
      setShowWrongAnswer(false);
      onNextQuestion(); // Переход к следующему вопросу
   };

   interface Word {
      id: number;
      rus_plaintext: string;
      plaintext: string;
   }

   function shuffleArray<T>(array: T[]): T[] {
      return array
         .map((item) => ({ ...item, sort: Math.random() }))
         .sort((a, b) => a.sort - b.sort)
         .map((item) => item);
   }

   const [shuffledTranslations, setShuffledTranslations] = useState<Word[]>([]);

   useEffect(() => {
      const translations = question.words.map((word: any) => ({
         id: word.id,
         plaintext: word.plaintext,
      }));
      const shuffled: any = shuffleArray(translations);
      setShuffledTranslations(shuffled);
   }, [question.words]);

   const [firstPairSelected, setFirstPairSelected] = useState({ id: 0 });
   const [secondPairSelected, setSecondPairSelected] = useState({ id: 0 });

   const [answeredPairs, setAnsweredPairs] = useState([]);

   const handleClick = ({ order, word }: { order: number; word: any }) => {
      if (order == 1) {
         // @ts-ignore
         if (firstPairSelected?.id == word.id) {
            setFirstPairSelected({ id: 0 });
         } else {
            setFirstPairSelected(word);
         }
      } else {
         // @ts-ignore
         if (secondPairSelected?.id == word.id) {
            setSecondPairSelected({ id: 0 });
         } else {
            setSecondPairSelected(word);
         }
      }

      if (order == 1 && secondPairSelected.id !== 0) {
         checkAnswer(order, word);
         return;
      }

      if (order == 2 && firstPairSelected.id !== 0) {
         checkAnswer(order, word);
         return;
      }
   };

   const checkAnswer = (order: number, word: any) => {
      if (order == 1) {
         if (word.id == secondPairSelected.id) {
            // @ts-ignore
            setAnsweredPairs((prev) => [...prev, word.id]);
            setFirstPairSelected({ id: 0 });
            setSecondPairSelected({ id: 0 });
            PlayCorrectSound();
         } else {
            setFirstPairSelected({ id: 0 });
            setSecondPairSelected({ id: 0 });
            PlayWrongSound();
         }
      }

      if (order == 2) {
         if (word.id == firstPairSelected.id) {
            // @ts-ignore
            setAnsweredPairs((prev) => [...prev, word.id]);
            setFirstPairSelected({ id: 0 });
            setSecondPairSelected({ id: 0 });
            PlayCorrectSound();
         } else {
            setFirstPairSelected({ id: 0 });
            setSecondPairSelected({ id: 0 });
            PlayWrongSound();
         }
      }
   };

   return (
      <View
         style={{
            height: "100%",
         }}
      >
         <View
            style={{
               display: "flex",
               flexDirection: "column",
               justifyContent: "space-between",
               marginTop: 22,
               height: "80%",
            }}
         >
            <Text
               style={{
                  fontWeight: 700,
                  fontSize: 20,
                  color: "#242B35",
                  width: 300,
                  marginHorizontal: "auto",
               }}
            >
               {question.type == "kz-word-to-ru-word" ? "Выберите перевод" : ""}
               {question.type == "word-pair" ? "Выберите пары" : ""}
            </Text>

            <View style={{ width: "90%", marginHorizontal: "auto" }}>
               {question.words.map((word: Word, index: number) => {
                  const isAnswered = answeredPairs.some((item) => item == word.id);

                  const secondIsAnswered = answeredPairs.some((item) => item == shuffledTranslations[index]?.id);

                  return (
                     <View
                        key={word.id}
                        style={{
                           display: "flex",
                           flexDirection: "row",
                           justifyContent: "space-between",
                        }}
                     >
                        <PairWordButton
                           handlePress={() => {
                              handleClick({ order: 1, word: word });
                           }}
                           isPicked={firstPairSelected?.id == word.id ? true : false}
                           title={word.rus_plaintext}
                           isValid={!isAnswered}
                        />
                        <PairWordButton
                           handlePress={() => {
                              handleClick({
                                 order: 2,
                                 word: shuffledTranslations[index],
                              });
                           }}
                           isPicked={secondPairSelected?.id == shuffledTranslations[index]?.id ? true : false}
                           title={shuffledTranslations[index]?.plaintext}
                           isValid={!secondIsAnswered}
                        />
                     </View>
                  );
               })}
            </View>

            <ChoiceRequiredButton
               isValid={answeredPairs.length == question.words.length ? true : false}
               title="Далее"
               handlePress={handleNext}
            />
         </View>

         {showCorrectAnswer && <LessonAnswerBottom onNext={handleNext} question={question} isCorrect={true} />}
         {showWrongAnswer && <LessonAnswerBottom onNext={handleNext} question={question} isCorrect={false} />}
      </View>
   );
};

export default QuestionPairWords;
