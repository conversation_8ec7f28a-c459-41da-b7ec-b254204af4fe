# Руководство по реализации системы достижений

## Обзор

Система достижений полностью интегрирована в мобильное приложение и включает:

- ✅ Отображение достижений по категориям
- ✅ Реальный прогресс пользователя
- ✅ Ежедневные задания
- ✅ Push-уведомления о новых достижениях
- ✅ Автоматическое обновление при действиях пользователя
- ✅ Тестовые функции для разработки

## Структура файлов

### Основные компоненты
- `screens/Achievements/Achievements.tsx` - Главный экран с табами
- `screens/Achievements/AchievementsContent.tsx` - Отображение достижений
- `screens/Achievements/QuestContent.tsx` - Ежедневные задания
- `screens/Achievements/AchievementCard.tsx` - Карточка достижения
- `components/DailyQuesItem.tsx` - Элемент ежедневного задания

### Хуки и утилиты
- `hooks/useAchievementNotifications.ts` - Уведомления о достижениях
- `hooks/useAchievementActions.ts` - Действия пользователя
- `utils/service.ts` - API функции
- `types/module.ts` - TypeScript типы

## Типы достижений

### Поддерживаемые типы:
- `lessons` - Количество пройденных уроков
- `words` - Количество изученных слов  
- `streak` - Дни подряд занятий
- `modules` - Количество завершенных модулей
- `questions` - Количество отвеченных вопросов
- `accuracy` - Точность ответов
- `speed` - Скорость прохождения
- `consecutive` - Подряд правильных ответов

### Категории:
- `progress` - Достижения за обучение
- `vocabulary` - Достижения за изучение слов
- `consistency` - Достижения за регулярность
- `accuracy` - Достижения за правильные ответы
- `speed` - Достижения за быстрое прохождение
- `general` - Специальные достижения

## API Эндпоинты

### Достижения
- `GET /v1/achievements-all` - Все доступные достижения
- `GET /v1/achievements/{user_id}` - Достижения пользователя
- `PATCH /v1/achievements` - Обновление прогресса

### Ежедневные задания
- `GET /v1/daily-quests/{user_id}` - Задания пользователя
- `GET /v1/daily-quests-all` - Все доступные задания
- `PATCH /v1/daily-quests` - Обновление прогресса
- `GET /v1/daily-quests/stats/{user_id}` - Статистика заданий

### Действия пользователя
- `POST /v1/word/learned` - Отметка изученных слов
- `POST /v1/question/answered` - Ответ на вопрос
- `POST /v1/progress/save` - Сохранение прогресса урока

## Использование в компонентах

### Интеграция с уроками

```typescript
import { useLessonCompletion } from '../hooks/useAchievementActions';

const LessonComponent = () => {
  const { completeLessonWithAchievements } = useLessonCompletion();

  const handleLessonComplete = async () => {
    await completeLessonWithAchievements({
      moduleId: 1,
      mistakenQuestionIds: [2, 5],
      timeSpent: 120,
      wordsLearned: [1, 2, 3],
      questionsAnswered: [
        { questionId: 1, isCorrect: true },
        { questionId: 2, isCorrect: false },
      ],
    });
  };
};
```

### Отслеживание достижений

```typescript
import { useAchievementActions } from '../hooks/useAchievementActions';

const Component = () => {
  const { markWordsLearned, markQuestionAnswer } = useAchievementActions();

  // Отметить слова как изученные
  const handleWordsLearned = () => {
    markWordsLearned([1, 2, 3]);
  };

  // Отметить ответ на вопрос
  const handleQuestionAnswer = () => {
    markQuestionAnswer(1, true);
  };
};
```

## Тестирование

### В режиме разработки
В `AchievementsContent` доступны кнопки для тестирования:
- "Завершить урок" - симулирует завершение урока
- "Изучить слова" - симулирует изучение слов
- "Ответить на вопросы" - симулирует ответы на вопросы
- "Обновить данные" - обновляет данные с сервера

### Проверка уведомлений
Уведомления автоматически показываются при получении новых достижений.

## Настройка уведомлений

### Разрешения
Убедитесь, что у приложения есть разрешения на уведомления:

```typescript
import * as Notifications from 'expo-notifications';

const requestPermissions = async () => {
  const { status } = await Notifications.requestPermissionsAsync();
  return status === 'granted';
};
```

### Кастомизация
Настройки уведомлений в `useAchievementNotifications.ts`:
- Звук: `shouldPlaySound: true`
- Алерт: `shouldShowAlert: true`
- Бейдж: `shouldSetBadge: false`

## Производительность

### Кэширование
- Данные достижений кэшируются на уровне API
- Локальное состояние обновляется только при изменениях

### Оптимизация
- Используйте `useCallback` для функций обновления
- Избегайте частых запросов к API
- Обновляйте данные только при необходимости

## Расширение системы

### Добавление нового типа достижения
1. Добавить тип в `types/module.ts`
2. Обновить `getAchievementsByType` в `AchievementsContent`
3. Добавить обработку в `useAchievementActions`
4. Создать достижения через админ API

### Добавление новой категории
1. Добавить категорию в типы
2. Обновить цветовую схему в `getAchievementsByType`
3. Добавить секцию в `AchievementsContent`

## Troubleshooting

### Достижения не обновляются
- Проверьте подключение к API
- Убедитесь, что пользователь авторизован
- Проверьте логи в консоли

### Уведомления не приходят
- Проверьте разрешения на уведомления
- Убедитесь, что хук `useAchievementNotifications` подключен
- Проверьте настройки уведомлений в системе

### Данные не загружаются
- Проверьте URL API в `utils/service.ts`
- Убедитесь, что токен авторизации действителен
- Проверьте сетевое подключение
