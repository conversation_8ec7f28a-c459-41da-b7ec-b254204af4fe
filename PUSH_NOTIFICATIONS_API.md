# Push Notifications API Documentation

## Обзор

Система push-уведомлений в Kazakh-Lingo позволяет отправлять уведомления на мобильные устройства пользователей через Firebase Cloud Messaging (FCM). Система поддерживает различные типы уведомлений и настройки пользователей.

## Архитектура

### Компоненты системы:
- **Firebase Cloud Messaging (FCM)** - основной сервис для отправки push-уведомлений
- **RabbitMQ** - асинхронная обработка очереди уведомлений
- **PostgreSQL** - хранение токенов устройств, настроек и истории уведомлений
- **Система шаблонов** - управление содержимым уведомлений

### Типы уведомлений:
- `achievement` - уведомления о достижениях
- `reminder` - напоминания о занятиях
- `new_content` - уведомления о новом контенте
- `progress` - уведомления о прогрессе
- `welcome` - приветственные уведомления
- `streak_lost` - уведомления о прерванной серии
- `streak_milestone` - уведомления о достижении серии

## API Endpoints

### 1. Регистрация токена устройства

**POST** `/v1/notifications/device-token`

Регистрирует новый FCM токен устройства для пользователя.

**Требует аутентификации**: Да

**Параметры запроса:**
```json
{
  "token": "string (required) - FCM токен устройства",
  "device_type": "string (required) - тип устройства: ios, android, web",
  "device_id": "string (optional) - уникальный ID устройства",
  "device_name": "string (optional) - название устройства",
  "app_version": "string (optional) - версия приложения"
}
```

**Ответ (201 Created):**
```json
{
  "device_token": {
    "id": 1,
    "user_id": 123,
    "token": "fcm_token_here",
    "device_type": "ios",
    "device_id": "iPhone_12_Pro",
    "device_name": "iPhone пользователя",
    "app_version": "1.0.0",
    "is_active": true,
    "last_used_at": "2024-01-01T12:00:00Z",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 2. Получение токенов устройств пользователя

**GET** `/v1/notifications/device-tokens`

Возвращает все активные токены устройств текущего пользователя.

**Требует аутентификации**: Да

**Ответ (200 OK):**
```json
{
  "device_tokens": [
    {
      "id": 1,
      "user_id": 123,
      "token": "fcm_token_here",
      "device_type": "ios",
      "device_id": "iPhone_12_Pro",
      "device_name": "iPhone пользователя",
      "app_version": "1.0.0",
      "is_active": true,
      "last_used_at": "2024-01-01T12:00:00Z",
      "created_at": "2024-01-01T12:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z"
    }
  ]
}
```

### 3. Деактивация токена устройства

**DELETE** `/v1/notifications/device-token`

Деактивирует токен устройства (например, при выходе из приложения).

**Требует аутентификации**: Да

**Параметры запроса:**
```json
{
  "token": "string (required) - FCM токен для деактивации"
}
```

**Ответ (200 OK):**
```json
{
  "message": "device token deactivated"
}
```

### 4. Получение настроек уведомлений

**GET** `/v1/notifications/settings`

Возвращает настройки уведомлений пользователя.

**Требует аутентификации**: Да

**Ответ (200 OK):**
```json
{
  "notification_settings": {
    "id": 1,
    "user_id": 123,
    "push_enabled": true,
    "achievements_enabled": true,
    "reminders_enabled": true,
    "new_content_enabled": true,
    "daily_reminder_time": "09:00:00",
    "timezone": "UTC",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 5. Обновление настроек уведомлений

**PUT** `/v1/notifications/settings`

Обновляет настройки уведомлений пользователя.

**Требует аутентификации**: Да

**Параметры запроса:**
```json
{
  "push_enabled": "boolean (optional) - включить/выключить push-уведомления",
  "achievements_enabled": "boolean (optional) - уведомления о достижениях",
  "reminders_enabled": "boolean (optional) - напоминания",
  "new_content_enabled": "boolean (optional) - уведомления о новом контенте",
  "daily_reminder_time": "string (optional) - время ежедневного напоминания (HH:MM:SS)",
  "timezone": "string (optional) - часовой пояс пользователя"
}
```

**Ответ (200 OK):**
```json
{
  "notification_settings": {
    "id": 1,
    "user_id": 123,
    "push_enabled": false,
    "achievements_enabled": true,
    "reminders_enabled": false,
    "new_content_enabled": true,
    "daily_reminder_time": "10:00:00",
    "timezone": "Asia/Almaty",
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 6. История уведомлений

**GET** `/v1/notifications/history?page=1&limit=20`

Возвращает историю отправленных уведомлений пользователя.

**Требует аутентификации**: Да

**Query параметры:**
- `page` (optional) - номер страницы (по умолчанию: 1)
- `limit` (optional) - количество записей на странице (по умолчанию: 20, максимум: 100)

**Ответ (200 OK):**
```json
{
  "notification_history": [
    {
      "id": 1,
      "user_id": 123,
      "device_token_id": 1,
      "notification_type": "achievement",
      "title": "🎉 Новое достижение!",
      "body": "Поздравляем! Вы получили достижение: Первые шаги",
      "data": {
        "type": "achievement",
        "user_id": "123",
        "timestamp": "2024-01-01T12:00:00Z",
        "AchievementName": "Первые шаги"
      },
      "status": "sent",
      "fcm_message_id": "fcm_message_id_here",
      "error_message": null,
      "sent_at": "2024-01-01T12:00:00Z",
      "created_at": "2024-01-01T12:00:00Z"
    }
  ],
  "page": 1,
  "limit": 20
}
```

### 7. Тестовое уведомление (только для разработки)

**POST** `/v1/notifications/test`

Отправляет тестовое уведомление текущему пользователю.

**Требует аутентификации**: Да

**Параметры запроса:**
```json
{
  "type": "string (required) - тип уведомления",
  "title": "string (required) - заголовок уведомления",
  "body": "string (required) - текст уведомления",
  "image_url": "string (optional) - URL изображения",
  "data": "object (optional) - дополнительные данные"
}
```

**Ответ (200 OK):**
```json
{
  "message": "test notification sent"
}
```

## Интеграция с мобильным приложением

### Настройка FCM в мобильном приложении

1. **Android**: Добавьте `google-services.json` в проект
2. **iOS**: Добавьте `GoogleService-Info.plist` в проект
3. **Получение токена**: Используйте FCM SDK для получения токена устройства
4. **Регистрация токена**: Отправьте токен на сервер через API

### Пример регистрации токена (JavaScript/React Native):

```javascript
import messaging from '@react-native-firebase/messaging';

// Получение токена
const getToken = async () => {
  const token = await messaging().getToken();
  
  // Отправка токена на сервер
  await fetch('/v1/notifications/device-token', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${userToken}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      token: token,
      device_type: Platform.OS, // 'ios' или 'android'
      device_name: DeviceInfo.getDeviceName(),
      app_version: DeviceInfo.getVersion(),
    }),
  });
};
```

## Автоматические уведомления

Система автоматически отправляет уведомления в следующих случаях:

1. **Приветственное уведомление** - при регистрации нового пользователя
2. **Уведомления о достижениях** - при получении нового достижения
3. **Уведомления о прогрессе** - каждые 5 пройденных модулей
4. **Напоминания** - согласно настройкам пользователя (требует дополнительной настройки cron-задач)

## Безопасность

- Все endpoints требуют аутентификации через Bearer токен
- Пользователи могут управлять только своими токенами и настройками
- FCM токены автоматически деактивируются при неактивности
- Поддержка отзыва токенов при выходе из приложения

## Мониторинг и логирование

- Все отправленные уведомления сохраняются в истории
- Ошибки отправки логируются с подробной информацией
- Метрики доставки доступны через FCM консоль
- Асинхронная обработка через RabbitMQ обеспечивает надежность
