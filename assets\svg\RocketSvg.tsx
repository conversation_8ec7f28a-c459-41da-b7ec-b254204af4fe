import React from "react";
import { G, Path, Svg } from "react-native-svg";

export default function RocketSvg() {
   return (
      <Svg width="26" height="26" viewBox="0 0 26 26" fill="none">
         <Path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M15.5486 17.1586L21.639 11.0864C22.5162 10.2119 22.9547 9.77466 23.1857 9.21864C23.4167 8.66262 23.4167 8.04427 23.4167 6.80758V6.21676C23.4167 4.31505 23.4167 3.36419 22.8242 2.7734C22.2316 2.18262 21.2779 2.18262 19.3705 2.18262H18.7779C17.5375 2.18262 16.9172 2.18262 16.3595 2.41293C15.8019 2.64324 15.3633 3.08048 14.4862 3.95497L8.3957 10.0272C7.37079 11.049 6.73528 11.6826 6.48919 12.2946C6.41144 12.4879 6.37256 12.6791 6.37256 12.8797C6.37256 13.7152 7.04694 14.3876 8.3957 15.7323L8.57697 15.913L10.7004 13.7579C11.0033 13.4506 11.4979 13.4469 11.8053 13.7497C12.1126 14.0526 12.1163 14.5472 11.8134 14.8546L9.6835 17.0163L9.82627 17.1586C11.175 18.5033 11.8494 19.1757 12.6874 19.1757C12.8726 19.1757 13.0498 19.1428 13.228 19.0772C13.8564 18.8457 14.4978 18.2062 15.5486 17.1586ZM18.4098 10.0276C17.6198 10.8153 16.3388 10.8153 15.5487 10.0276C14.7586 9.23991 14.7586 7.96277 15.5487 7.17506C16.3388 6.38734 17.6198 6.38734 18.4098 7.17506C19.1999 7.96277 19.1999 9.23991 18.4098 10.0276Z"
            fill="#ED8A19"
         />
         <G opacity="0.5">
            <Path
               d="M9.91088 6.311L7.26177 8.95216C6.77484 9.43761 6.32816 9.88293 5.97541 10.2855C5.74889 10.544 5.52251 10.827 5.33076 11.1396L5.30494 11.1139C5.25595 11.065 5.23142 11.0406 5.20682 11.0167C4.74637 10.5699 4.20476 10.2147 3.61095 9.97006C3.57922 9.95699 3.54701 9.94423 3.4826 9.91871L3.08818 9.76242C2.55384 9.55069 2.41144 8.86281 2.81802 8.45746C3.9849 7.29411 5.38591 5.89734 6.06205 5.6168C6.65834 5.36939 7.30249 5.28707 7.92375 5.37888C8.49298 5.463 9.03148 5.7554 9.91088 6.311Z"
               fill="#ED8A19"
            />
            <Path
               d="M14.4342 20.2174C14.6477 20.434 14.7895 20.5871 14.9176 20.7506C15.0867 20.9664 15.238 21.1956 15.3698 21.4359C15.5181 21.7064 15.6333 21.9953 15.8637 22.5733C16.0512 23.0437 16.6743 23.1681 17.0362 22.8073L17.1237 22.72C18.2906 21.5566 19.6915 20.1598 19.9729 19.4857C20.2211 18.8912 20.3036 18.249 20.2115 17.6296C20.1272 17.0621 19.834 16.5253 19.2768 15.6488L16.6189 18.2987C16.1209 18.7952 15.6643 19.2505 15.2514 19.6071C15.0039 19.8208 14.7332 20.0345 14.4342 20.2174Z"
               fill="#ED8A19"
            />
         </G>
      </Svg>
   );
}
