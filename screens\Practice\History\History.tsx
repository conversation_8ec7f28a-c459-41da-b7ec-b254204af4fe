import { StatusBar, StyleSheet, Text, View, SafeAreaView } from "react-native";
import React from "react";

import HistoryHeader from "./HistoryHeader";
import { SafeAreaProvider } from "react-native-safe-area-context";
import HistoryFilter from "./HistoryFilter";
import HistoryContent from "./HistoryContent";

export default function History() {
   return (
      <SafeAreaProvider style={{ backgroundColor: "#fff" }}>
         <StatusBar barStyle="dark-content" />
         <SafeAreaView>
            <HistoryHeader />
            <HistoryFilter />
            <HistoryContent />
         </SafeAreaView>

         {/* <Text>asdkasbdjbh</Text> */}
      </SafeAreaProvider>
   );
}
