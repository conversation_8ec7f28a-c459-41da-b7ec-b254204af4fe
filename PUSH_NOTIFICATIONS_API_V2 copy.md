# 📱 Push Notifications API v2.0

## 🚀 Обзор

Полноценная система push-уведомлений с поддержкой:
- ✅ **Индивидуальные уведомления** - одному пользователю
- ✅ **Массовые уведомления** - списку пользователей
- ✅ **Групповые уведомления** - по критериям (все, роль, уровень, активность)
- ✅ **Админские уведомления** - с правами администратора
- ✅ **Отложенные уведомления** - запланированная отправка
- ✅ **Автоматическое определение сервиса** - Expo Push Service / Firebase FCM
- ✅ **Детальная статистика** - успешность доставки

## 🔧 Базовый URL

```
Production: https://your-domain.com/v1/notifications
Development: http://localhost:8080/v1/notifications
```

## 🔐 Аутентификация

Все endpoints требуют JWT токен в заголовке:
```
Authorization: Bearer <your_jwt_token>
```

## 📋 Endpoints

### 1. 👤 Индивидуальное уведомление

**POST** `/v1/notifications/send`

Отправляет уведомление одному пользователю.

**Требует аутентификации**: Да

**Тело запроса:**
```json
{
  "user_id": 123,
  "type": "achievement",
  "title": "🎉 Новое достижение!",
  "body": "Поздравляем! Вы получили достижение: Первые шаги",
  "image_url": "https://example.com/achievement.png",
  "data": {
    "achievement_id": "1",
    "points": "100"
  },
  "send_at": "2024-01-01T15:00:00Z"
}
```

**Ответ (200 OK):**
```json
{
  "status": "success",
  "message": "Notification sent successfully"
}
```

### 2. 📢 Массовые уведомления

**POST** `/v1/notifications/bulk`

Отправляет уведомления списку пользователей.

**Требует аутентификации**: Да

**Тело запроса:**
```json
{
  "user_ids": [123, 456, 789],
  "type": "new_content",
  "title": "📚 Новый урок доступен!",
  "body": "Изучите новый урок по казахскому языку",
  "image_url": "https://example.com/lesson.png",
  "data": {
    "lesson_id": "42",
    "module_id": "5"
  },
  "send_at": "2024-01-01T15:00:00Z"
}
```

**Ответ (200 OK):**
```json
{
  "status": "success",
  "message": "Bulk notification sent",
  "stats": {
    "total_sent": 3,
    "total_success": 2,
    "total_failed": 1,
    "success_rate": 66.67,
    "service_used": "Expo Push Service",
    "sent_at": "2024-01-01T15:00:00Z"
  }
}
```

### 3. 👥 Групповые уведомления

**POST** `/v1/notifications/group`

Отправляет уведомления группе пользователей по критериям.

**Требует аутентификации**: Да

**Тело запроса:**
```json
{
  "group_type": "level",
  "group_value": "beginner",
  "type": "reminder",
  "title": "⏰ Время заниматься!",
  "body": "Не забудьте выполнить ежедневные упражнения",
  "data": {
    "reminder_type": "daily_practice"
  }
}
```

**Типы групп:**
- `all` - все пользователи
- `role` - по роли (student, teacher, admin)
- `level` - по уровню (beginner, intermediate, advanced)
- `active` - активные пользователи (заходили в последние 7 дней)
- `inactive` - неактивные пользователи

**Ответ (200 OK):**
```json
{
  "status": "success",
  "message": "Group notification sent",
  "stats": {
    "total_sent": 150,
    "total_success": 142,
    "total_failed": 8,
    "success_rate": 94.67,
    "service_used": "Expo Push Service",
    "sent_at": "2024-01-01T15:00:00Z"
  }
}
```

### 4. 🛡️ Админские уведомления

**POST** `/v1/notifications/admin`

Отправляет уведомления от имени администратора.

**Требует аутентификации**: Да (с правами администратора)

**Тело запроса:**
```json
{
  "target_type": "all",
  "type": "announcement",
  "title": "📢 Важное объявление",
  "body": "Сервер будет недоступен завтра с 02:00 до 04:00 по причине технического обслуживания",
  "data": {
    "maintenance": "true",
    "start_time": "2024-01-02T02:00:00Z",
    "end_time": "2024-01-02T04:00:00Z"
  }
}
```

**Типы целей:**
- `user` - конкретные пользователи (требует `user_ids`)
- `bulk` - список пользователей (требует `user_ids`)
- `group` - группа пользователей (требует `group_type`)
- `all` - все пользователи

**Ответ (200 OK):**
```json
{
  "status": "success",
  "message": "Admin notification sent",
  "stats": {
    "total_sent": 1250,
    "total_success": 1198,
    "total_failed": 52,
    "success_rate": 95.84,
    "service_used": "Expo Push Service",
    "sent_at": "2024-01-01T15:00:00Z"
  },
  "admin_user_id": 1
}
```

### 5. 📊 Статистика уведомлений

**GET** `/v1/notifications/stats?period=7d`

Возвращает статистику отправленных уведомлений.

**Требует аутентификации**: Да (администратор)

**Query параметры:**
- `period` - период (1d, 7d, 30d, 90d)
- `type` - тип уведомлений (optional)

**Ответ (200 OK):**
```json
{
  "stats": {
    "total_sent": 5420,
    "total_success": 5124,
    "total_failed": 296,
    "success_rate": 94.54,
    "by_type": {
      "achievement": 1250,
      "reminder": 2100,
      "new_content": 1520,
      "announcement": 550
    },
    "by_service": {
      "expo_push_service": 4890,
      "firebase_fcm": 530
    }
  }
}
```

## 🔧 Типы уведомлений

```json
{
  "achievement": "Достижения и награды",
  "reminder": "Напоминания о занятиях",
  "new_content": "Новый контент",
  "progress": "Прогресс обучения",
  "welcome": "Приветственные сообщения",
  "streak_lost": "Потеря серии",
  "streak_milestone": "Достижение в серии",
  "announcement": "Объявления администрации"
}
```

## ⏰ Отложенные уведомления

Все endpoints поддерживают параметр `send_at` для отложенной отправки:

```json
{
  "send_at": "2024-01-01T15:00:00Z"
}
```

Формат: RFC3339 (ISO 8601)

## 📱 Поддерживаемые платформы

### Expo Go (разработка)
- Токены: `ExponentPushToken[...]`
- Сервис: Expo Push Service
- Автоматическое определение

### Standalone приложения (продакшн)
- Android: FCM-подобные токены через Expo
- iOS: Expo токены или APNs
- Автоматическое определение сервиса

## 🚨 Ошибки

### 400 Bad Request
```json
{
  "error": {
    "user_ids": ["must not be empty"],
    "title": ["must be provided"]
  }
}
```

### 401 Unauthorized
```json
{
  "error": "authentication required"
}
```

### 403 Forbidden
```json
{
  "error": "admin privileges required"
}
```

### 500 Internal Server Error
```json
{
  "error": "failed to send notifications"
}
```

## 📈 Лимиты

- **Bulk уведомления**: до 1000 пользователей за запрос
- **Rate limiting**: 100 запросов в минуту на пользователя
- **Размер payload**: до 4KB
- **Отложенная отправка**: до 30 дней в будущем

## 🔍 Мониторинг

- **Логи**: MongoDB коллекция `logs`
- **Метрики**: `/debug/vars` endpoint
- **История**: `/v1/notifications/history` endpoint

## 🎯 Примеры использования

### Отправка достижения
```bash
curl -X POST http://localhost:8080/v1/notifications/send \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 123,
    "type": "achievement",
    "title": "🎉 Новое достижение!",
    "body": "Вы завершили 10 уроков подряд!"
  }'
```

### Массовая рассылка
```bash
curl -X POST http://localhost:8080/v1/notifications/bulk \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_ids": [123, 456, 789],
    "type": "new_content",
    "title": "📚 Новый урок!",
    "body": "Изучите новый урок по грамматике"
  }'
```

### Админское объявление
```bash
curl -X POST http://localhost:8080/v1/notifications/admin \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "target_type": "all",
    "type": "announcement",
    "title": "📢 Объявление",
    "body": "Новая версия приложения доступна!"
  }'
```
