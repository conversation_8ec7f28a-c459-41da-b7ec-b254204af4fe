import Svg, { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Defs, Rect } from "react-native-svg";

const ListeningSvg = () => (
   <Svg width="66" height="57" viewBox="0 0 66 57" fill="none">
      <G clip-path="url(#clip0_294_1940)">
         <Path
            opacity="0.5"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12.5334 34.3079C9.32608 22.8403 16.0224 10.944 27.4899 7.73672C38.9575 4.52944 50.8538 11.2257 54.0611 22.6933L55.7244 28.6406C55.8745 28.5352 56.0448 28.4531 56.2314 28.4008C57.1136 28.1541 58.0287 28.6692 58.2754 29.5513L59.4666 33.8106C59.7133 34.6927 59.1982 35.6078 58.3161 35.8545C57.434 36.1012 56.5189 35.5861 56.2722 34.704L55.9744 33.6392L53.8447 34.2348L50.8666 23.5867C48.1528 13.8834 38.0867 8.21731 28.3833 10.9312C18.68 13.645 13.0139 23.7111 15.7278 33.4144L18.7059 44.0626L16.5763 44.6582L16.8741 45.723C17.1208 46.6051 16.6057 47.5202 15.7236 47.7669C14.8415 48.0137 13.9264 47.4986 13.6797 46.6164L12.4884 42.3572C12.2417 41.4751 12.7568 40.56 13.6389 40.3133C13.8256 40.261 14.0138 40.243 14.1967 40.2552L12.5334 34.3079Z"
            fill="#32D583"
         />
         <Path
            d="M25.9988 35.2465C25.4929 33.4377 25.24 32.5333 24.6175 32.0293C24.3043 31.7757 23.9329 31.5976 23.5328 31.5093C22.7378 31.3336 21.8274 31.6802 20.0066 32.3734C16.9383 33.5415 15.4042 34.1255 14.4961 35.228C14.0365 35.7859 13.6992 36.4278 13.5054 37.1131C13.1223 38.4673 13.5486 39.9913 14.401 43.0392L15.4372 46.7439C16.2811 49.7615 16.7031 51.2702 17.7496 52.2339C18.1413 52.5946 18.5894 52.8934 19.0779 53.1196C20.3833 53.7241 22 53.5747 25.2336 53.2761C27.5091 53.0659 28.6469 52.9608 29.3294 52.321C29.5812 52.085 29.7836 51.8041 29.9251 51.4944C30.3083 50.6551 30.0114 49.5934 29.4175 47.4699L25.9988 35.2465Z"
            fill="#32D583"
         />
         <Path
            d="M43.0358 30.4815C42.5299 28.6727 42.277 27.7683 42.5476 27.0146C42.6838 26.6353 42.909 26.2903 43.2051 26.0072C43.7937 25.4447 44.7518 25.2687 46.6681 24.9166C49.8971 24.3235 51.5116 24.0269 52.8599 24.4983C53.5422 24.7368 54.1636 25.1105 54.6849 25.5959C55.7149 26.5549 56.1411 28.0788 56.9935 31.1267L58.0297 34.8315C58.8736 37.849 59.2956 39.3578 58.9009 40.7245C58.7532 41.2361 58.5251 41.724 58.2248 42.1709C57.4225 43.3648 55.9628 44.0759 53.0434 45.4981C50.989 46.4989 49.9618 46.9993 49.0465 46.8065C48.7087 46.7353 48.39 46.6002 48.1084 46.4088C47.3453 45.8902 47.0484 44.8284 46.4545 42.7049L43.0358 30.4815Z"
            fill="#32D583"
         />
      </G>
      <Defs>
         <ClipPath id="clip0_294_1940">
            <Rect
               width="53.0724"
               height="53.0724"
               fill="white"
               transform="translate(0.593994 10.092) rotate(-15.6254)"
            />
         </ClipPath>
      </Defs>
   </Svg>
);

export default ListeningSvg;
