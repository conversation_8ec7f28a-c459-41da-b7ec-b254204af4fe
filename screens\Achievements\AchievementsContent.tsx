import { ScrollView, Text, View } from "react-native";
import React, { useEffect, useState } from "react";
import { AchievementCard } from "./AchievementCard";
import { <PERSON>, <PERSON> } from "../../assets/image";
import { Achievement } from "../../types/module";
import { GetRequest } from "../../utils/service";
import { useAuth } from "../../context/AuthContext";

export default function AchievementsContent() {
   const { authState } = useAuth();

   const [achievements, setAchievements] = useState<Achievement[]>([]);
   const [userA, setUserA] = useState([]);
   const [isLoading, setIsLoading] = useState<Boolean>(true);

   useEffect(() => {
      const fetchData = async () => {
         try {
            const [achievementsResponse, userAchievementsResponse] = await Promise.all([
               GetRequest("/v1/achievements-all"),
               GetRequest(`/v1/achievements/${authState.user?.id}`),
            ]);

            setAchievements(achievementsResponse.achievements);
            setUserA(userAchievementsResponse.userAchievements);
            setIsLoading(false);
         } catch (error) {
            setIsLoading(false);
            console.error("Error fetching data:", error);
         }
      };

      fetchData();
   }, []);

   const getAchievementsByType = (achievements: Array<Achievement>, type: string) => {
      return achievements
         .filter((achievement) => achievement.type === type)
         .map((achievement) => {
            let bg_color = "#00AFCA";
            let text_color = "#FFFFFF";
            let image = Arrow;

            switch (achievement.type) {
               case "progress":
                  bg_color = "#00AFCA";
                  text_color = "#FFFFFF";
                  image = Boy;
                  break;
               case "temp":
                  bg_color = "#F7AD3A";
                  text_color = "#242B35";
                  image = Arrow;
                  break;
               default:
                  bg_color = "#00AFCA";
                  text_color = "#FFFFFF";
                  image = Arrow;
            }

            return {
               ...achievement,
               bg_color,
               text_color,
               image,
            };
         });
   };

   if (isLoading) {
      return;
   }

   return (
      <View style={{ backgroundColor: "#fff", height: "100%", flex: 1 }}>
         <View
            style={{
               width: "100%",
               display: "flex",
               flexDirection: "row",
               justifyContent: "flex-end",
            }}
         >
            <Text
               style={{
                  color: "#8E8E93",
                  fontWeight: 500,
                  fontSize: 14,
                  marginVertical: 13,
                  marginHorizontal: 8,
               }}
            >
               Открыто 0/25
            </Text>
         </View>

         <View>
            <Text
               style={{
                  fontWeight: 600,
                  fontSize: 20,
                  paddingHorizontal: 15,
                  color: "#242B35",
               }}
            >
               Ударный темп
            </Text>

            <ScrollView
               horizontal
               showsHorizontalScrollIndicator={false}
               style={{
                  backgroundColor: "",
                  overflow: "visible",
                  marginHorizontal: 10,
                  marginTop: 20,
               }}
            >
               {getAchievementsByType(achievements, "temp").map((item: Achievement, index) => {
                  return (
                     <AchievementCard
                        key={index}
                        image={item.image}
                        title={item.name}
                        maxCount={item.target}
                        currentCount={0}
                        bgColor={item.bg_color ?? "#fff"}
                        textColor={item.text_color ?? "#000"}
                     />
                  );
               })}
            </ScrollView>
         </View>

         <View>
            <Text
               style={{
                  fontWeight: 600,
                  fontSize: 20,
                  paddingHorizontal: 15,
                  color: "#242B35",
                  marginTop: 28,
               }}
            >
               Занятия
            </Text>

            <ScrollView
               horizontal
               showsHorizontalScrollIndicator={false}
               style={{
                  backgroundColor: "",
                  overflow: "visible",
                  marginHorizontal: 10,
                  marginTop: 20,
               }}
            >
               {getAchievementsByType(achievements, "progress").map((item: Achievement, index) => {
                  return (
                     <AchievementCard
                        key={index}
                        image={item.image}
                        title={item.name}
                        maxCount={item.target}
                        currentCount={0}
                        bgColor={item.bg_color ?? "#fff"}
                        textColor={item.text_color ?? "#000"}
                     />
                  );
               })}
            </ScrollView>
         </View>
      </View>
   );
}
