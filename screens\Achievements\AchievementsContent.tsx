import { ScrollView, Text, View, TouchableOpacity } from "react-native";
import React, { useEffect, useState } from "react";
import { AchievementCard } from "./AchievementCard";
import { <PERSON>, <PERSON> } from "../../assets/image";
import { Achievement, UserAchievement } from "../../types/module";
import { getAllAchievements, getUserAchievements } from "../../utils/service";
import { useAuth } from "../../context/AuthContext";
import { useAchievementNotifications, useAchievementProgress } from "../../hooks/useAchievementNotifications";
import { useAchievementActions } from "../../hooks/useAchievementActions";

export default function AchievementsContent() {
   const { authState } = useAuth();
   const { scheduleLocalAchievementNotification } = useAchievementNotifications();
   const { simulateActions } = useAchievementActions();

   const [achievements, setAchievements] = useState<Achievement[]>([]);
   const [userAchievements, setUserAchievements] = useState<UserAchievement[]>([]);
   const [isLoading, setIsLoading] = useState<boolean>(true);

   // Обработчик новых достижений
   const handleNewAchievement = (achievement: Achievement, reward: number) => {
      scheduleLocalAchievementNotification(achievement, reward);
   };

   // Отслеживание изменений в достижениях
   useAchievementProgress(userAchievements, handleNewAchievement);

   // Функция для обновления данных
   const refreshData = async () => {
      if (!authState.user?.id) return;

      try {
         const [achievementsResponse, userAchievementsResponse] = await Promise.all([
            getAllAchievements(),
            getUserAchievements(authState.user.id),
         ]);

         setAchievements(achievementsResponse.achievements);
         setUserAchievements(userAchievementsResponse.user_achievements);
      } catch (error) {
         console.error("Error refreshing data:", error);
      }
   };

   useEffect(() => {
      const fetchData = async () => {
         try {
            if (!authState.user?.id) {
               setIsLoading(false);
               return;
            }

            const [achievementsResponse, userAchievementsResponse] = await Promise.all([
               getAllAchievements(),
               getUserAchievements(authState.user.id),
            ]);

            setAchievements(achievementsResponse.achievements);
            setUserAchievements(userAchievementsResponse.user_achievements);
            setIsLoading(false);
         } catch (error) {
            setIsLoading(false);
            console.error("Error fetching data:", error);
         }
      };

      fetchData();
   }, [authState.user?.id]);

   const getAchievementsByType = (achievements: Array<Achievement>, type: string) => {
      return achievements
         .filter((achievement) => achievement.type === type)
         .map((achievement) => {
            // Найти прогресс пользователя для этого достижения
            const userAchievement = userAchievements.find(
               (ua) => ua.achievement_id === achievement.id
            );

            let bg_color = "#00AFCA";
            let text_color = "#FFFFFF";
            let image = Arrow;

            // Определить цвета и изображения на основе типа достижения
            switch (achievement.type) {
               case "lessons":
               case "modules":
                  bg_color = "#00AFCA";
                  text_color = "#FFFFFF";
                  image = Boy;
                  break;
               case "words":
                  bg_color = "#4CAF50";
                  text_color = "#FFFFFF";
                  image = Arrow;
                  break;
               case "streak":
                  bg_color = "#FF9800";
                  text_color = "#FFFFFF";
                  image = Arrow;
                  break;
               case "questions":
                  bg_color = "#9C27B0";
                  text_color = "#FFFFFF";
                  image = Arrow;
                  break;
               case "accuracy":
                  bg_color = "#2196F3";
                  text_color = "#FFFFFF";
                  image = Arrow;
                  break;
               case "speed":
                  bg_color = "#F44336";
                  text_color = "#FFFFFF";
                  image = Arrow;
                  break;
               case "consecutive":
                  bg_color = "#607D8B";
                  text_color = "#FFFFFF";
                  image = Arrow;
                  break;
               default:
                  bg_color = "#00AFCA";
                  text_color = "#FFFFFF";
                  image = Arrow;
            }

            return {
               ...achievement,
               bg_color,
               text_color,
               image,
               progress: userAchievement?.progress || 0,
               achieved: userAchievement?.achieved || false,
            };
         });
   };

   // Подсчет статистики
   const completedAchievements = userAchievements.filter(ua => ua.achieved).length;
   const totalAchievements = achievements.length;

   if (isLoading) {
      return (
         <View style={{ backgroundColor: "#fff", height: "100%", flex: 1, justifyContent: "center", alignItems: "center" }}>
            <Text>Загрузка достижений...</Text>
         </View>
      );
   }

   return (
      <View style={{ backgroundColor: "#fff", height: "100%", flex: 1 }}>
         <View
            style={{
               width: "100%",
               display: "flex",
               flexDirection: "row",
               justifyContent: "flex-end",
            }}
         >
            <Text
               style={{
                  color: "#8E8E93",
                  fontWeight: 500,
                  fontSize: 14,
                  marginVertical: 13,
                  marginHorizontal: 8,
               }}
            >
               Открыто {completedAchievements}/{totalAchievements}
            </Text>
         </View>

         {/* Достижения за уроки */}
         <View>
            <Text
               style={{
                  fontWeight: 600,
                  fontSize: 20,
                  paddingHorizontal: 15,
                  color: "#242B35",
               }}
            >
               Уроки
            </Text>

            <ScrollView
               horizontal
               showsHorizontalScrollIndicator={false}
               style={{
                  backgroundColor: "",
                  overflow: "visible",
                  marginHorizontal: 10,
                  marginTop: 20,
               }}
            >
               {getAchievementsByType(achievements, "lessons").map((item: Achievement, index) => {
                  return (
                     <AchievementCard
                        key={index}
                        image={item.image}
                        title={item.name}
                        maxCount={item.target}
                        currentCount={item.progress || 0}
                        isPassed={item.achieved}
                        bgColor={item.bg_color ?? "#fff"}
                        textColor={item.text_color ?? "#000"}
                        reward={item.reward}
                        icon={item.icon}
                     />
                  );
               })}
            </ScrollView>
         </View>

         {/* Достижения за слова */}
         <View>
            <Text
               style={{
                  fontWeight: 600,
                  fontSize: 20,
                  paddingHorizontal: 15,
                  color: "#242B35",
                  marginTop: 28,
               }}
            >
               Словарь
            </Text>

            <ScrollView
               horizontal
               showsHorizontalScrollIndicator={false}
               style={{
                  backgroundColor: "",
                  overflow: "visible",
                  marginHorizontal: 10,
                  marginTop: 20,
               }}
            >
               {getAchievementsByType(achievements, "words").map((item: Achievement, index) => {
                  return (
                     <AchievementCard
                        key={index}
                        image={item.image}
                        title={item.name}
                        maxCount={item.target}
                        currentCount={item.progress || 0}
                        isPassed={item.achieved}
                        bgColor={item.bg_color ?? "#fff"}
                        textColor={item.text_color ?? "#000"}
                        reward={item.reward}
                        icon={item.icon}
                     />
                  );
               })}
            </ScrollView>
         </View>

         {/* Достижения за стрики */}
         <View>
            <Text
               style={{
                  fontWeight: 600,
                  fontSize: 20,
                  paddingHorizontal: 15,
                  color: "#242B35",
                  marginTop: 28,
               }}
            >
               Постоянство
            </Text>

            <ScrollView
               horizontal
               showsHorizontalScrollIndicator={false}
               style={{
                  backgroundColor: "",
                  overflow: "visible",
                  marginHorizontal: 10,
                  marginTop: 20,
               }}
            >
               {getAchievementsByType(achievements, "streak").map((item: Achievement, index) => {
                  return (
                     <AchievementCard
                        key={index}
                        image={item.image}
                        title={item.name}
                        maxCount={item.target}
                        currentCount={item.progress || 0}
                        isPassed={item.achieved}
                        bgColor={item.bg_color ?? "#fff"}
                        textColor={item.text_color ?? "#000"}
                        reward={item.reward}
                        icon={item.icon}
                     />
                  );
               })}
            </ScrollView>
         </View>

         {/* Кнопки для тестирования (можно удалить в продакшене) */}
         {__DEV__ && (
            <View style={{ padding: 20, backgroundColor: "#F5F5F5", marginTop: 20 }}>
               <Text style={{ fontWeight: "600", fontSize: 16, marginBottom: 10, textAlign: "center" }}>
                  Тестирование достижений
               </Text>

               <View style={{ flexDirection: "row", justifyContent: "space-around", flexWrap: "wrap" }}>
                  <TouchableOpacity
                     style={{
                        backgroundColor: "#00AFCA",
                        padding: 10,
                        borderRadius: 8,
                        margin: 5,
                        minWidth: 100,
                     }}
                     onPress={async () => {
                        await simulateActions.completeLesson();
                        setTimeout(refreshData, 1000);
                     }}
                  >
                     <Text style={{ color: "#fff", textAlign: "center", fontSize: 12 }}>
                        Завершить урок
                     </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                     style={{
                        backgroundColor: "#4CAF50",
                        padding: 10,
                        borderRadius: 8,
                        margin: 5,
                        minWidth: 100,
                     }}
                     onPress={async () => {
                        await simulateActions.learnWords();
                        setTimeout(refreshData, 1000);
                     }}
                  >
                     <Text style={{ color: "#fff", textAlign: "center", fontSize: 12 }}>
                        Изучить слова
                     </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                     style={{
                        backgroundColor: "#FF9800",
                        padding: 10,
                        borderRadius: 8,
                        margin: 5,
                        minWidth: 100,
                     }}
                     onPress={async () => {
                        await simulateActions.answerQuestions();
                        setTimeout(refreshData, 1000);
                     }}
                  >
                     <Text style={{ color: "#fff", textAlign: "center", fontSize: 12 }}>
                        Ответить на вопросы
                     </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                     style={{
                        backgroundColor: "#9C27B0",
                        padding: 10,
                        borderRadius: 8,
                        margin: 5,
                        minWidth: 100,
                     }}
                     onPress={refreshData}
                  >
                     <Text style={{ color: "#fff", textAlign: "center", fontSize: 12 }}>
                        Обновить данные
                     </Text>
                  </TouchableOpacity>
               </View>
            </View>
         )}
      </View>
   );
}
