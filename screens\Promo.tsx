import React from "react";
import { View, StyleSheet, Image, Dimensions, Text } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Ads, Book, Chess, Gamepad, Ornament } from "../assets/image";
import { useFonts } from "expo-font";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";
import PromoButton from "../components/ui/PromoButton";
import { KuraleRegular } from "../assets/fonts";

const { height, width } = Dimensions.get("window");

export default function Promo() {
   const [fontsLoaded, fontError] = useFonts({
      "Kurale-Regular": KuraleRegular,
   });

   if (!fontsLoaded && !fontError) {
      return null;
   }

   const content = [
      {
         image: Book,
         title: "Практические занятия",
         description: "100+ занятий от новичка до профи",
      },
      {
         image: Gamepad,
         title: "Интерактивность",
         description: "Учить слова с помощью игр, напоминаний и многое другое",
      },
      {
         image: Chess,
         title: "Дополнительный контент",
         description: "Вломитесь в рейтинг с безлимитным количеством попыток",
      },
      {
         image: Ads,
         title: "Нет рекламы",
         description: "Ваш процесс обучения больше ничего не прервет",
      },
   ];

   return (
      <SafeAreaProvider>
         <LinearGradient
            style={styles.container}
            colors={["#04212F", "#0086BB"]}
         >
            <SafeAreaView style={styles.safeArea}>
               <View style={styles.header}>
                  <Text style={styles.headerTextWhite}>Готовы изучать</Text>
                  <Text style={styles.headerTextAccent}>казахский?</Text>
               </View>

               <View style={styles.card}>
                  {content.map((item, index) => (
                     <View key={index} style={styles.cardItem}>
                        <Image style={styles.cardImage} source={item.image} />
                        <View style={styles.cardTextWrapper}>
                           <Text style={styles.cardTitle}>{item.title}</Text>
                           <Text style={styles.cardDescription}>
                              {item.description}
                           </Text>
                        </View>
                     </View>
                  ))}
               </View>

               <View style={styles.buttonWrapper}>
                  <PromoButton
                     handlePress={() => {}}
                     title="Посмотреть планы"
                  />
               </View>

               {/* Орнаменты */}
               <Image
                  source={Ornament}
                  style={[styles.ornament, styles.topOrnament]}
               />
               <Image
                  source={Ornament}
                  style={[styles.ornament, styles.bottomOrnament]}
               />
            </SafeAreaView>
         </LinearGradient>
      </SafeAreaProvider>
   );
}

const styles = StyleSheet.create({
   container: {
      flex: 1,
      width: "100%",
      height: "100%",
   },
   safeArea: {
      flex: 1,
   },
   header: {
      marginHorizontal: "auto",
      marginTop: 28,
      textAlign: "center",
   },
   headerTextWhite: {
      fontFamily: "Kurale-Regular",
      fontSize: 32,
      lineHeight: 47,
      color: "#FFFFFF",
      textAlign: "center",
   },
   headerTextAccent: {
      fontFamily: "Kurale-Regular",
      fontSize: 32,
      lineHeight: 47,
      color: "#ACEBFB",
      textAlign: "center",
      marginTop: 0,
   },
   card: {
      width: 339,
      height: 426,
      backgroundColor: "#fff",
      borderRadius: 15,
      marginHorizontal: "auto",
      marginTop: 38,
      shadowColor: "#BCDEED",
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 1,
      shadowRadius: 0,
      elevation: 4,
   },
   cardItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 30,
      paddingVertical: 30,
   },
   cardImage: {
      width: 54,
      height: 43.61,
      marginRight: 15,
   },
   cardTextWrapper: {
      flex: 1,
      justifyContent: "space-between",
   },
   cardTitle: {
      fontWeight: "700",
      fontSize: 16,
      lineHeight: 20,
      color: "#242B35",
   },
   cardDescription: {
      fontWeight: "500",
      fontSize: 11,
      color: "#0086BB",
      width: 220,
   },
   buttonWrapper: {
      marginTop: 70,
   },
   ornament: {
      position: "absolute",
      width: width * 1.08,
      height: height * 0.08,
      zIndex: -1,
   },
   topOrnament: {
      top: 165,
      left: 0,
   },
   bottomOrnament: {
      bottom: 160,
      right: 0,
   },
});
