import React, { useCallback, useRef } from "react";
import { SafeAreaView, Image, Text, View, StyleSheet, TouchableOpacity, StatusBar } from "react-native";
import ButtonGradient from "../components/ui/ButtonGradient";
import { Apple, Email, Facebook, Google, WelcomeImage } from "../assets/image";
import CustomBottomSheet from "../components/ui/CustomBottomSheet";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { BottomSheetModal } from "@gorhom/bottom-sheet";
import AuthButton from "../components/ui/AuthButton";
import { useNavigation } from "@react-navigation/native";

export default function Welcome() {
   const { navigate } = useNavigation();

   const bottomSheetModalRef = useRef<BottomSheetModal>(null);

   const handlePresentModalPress = useCallback(() => {
      bottomSheetModalRef.current?.present();
   }, []);

   const handleCloseSheet = useCallback(() => {
      bottomSheetModalRef.current?.close();
   }, []);

   const navToAuthType = (path: string) => {
      // @ts-ignore
      navigate(path);
   };

   return (
      <GestureHandlerRootView style={styles.flexContainer}>
         <StatusBar />
         <SafeAreaView style={styles.container}>
            <View style={styles.imageContainer}>
               <Image source={WelcomeImage} style={styles.image} />
            </View>

            <Text style={styles.title}>Сохраните свой прогресс!</Text>

            <Text style={styles.description}>
               Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce suscipit metus quis ex molestie, in consectetur quam
               eleifend. Nullam tempus dolor vel nisi bibendum tincidunt.
            </Text>

            <ButtonGradient handlePress={handlePresentModalPress} loading={false} title="Создать Аккаунт" />

            <TouchableOpacity
               onPress={() => {
                  // @ts-ignore
                  navigate("KnowledgeLevel");
               }}
               style={styles.linkContainer}
            >
               <Text style={styles.linkText}>Уже есть аккаунт</Text>
            </TouchableOpacity>

            <CustomBottomSheet ref={bottomSheetModalRef} onClose={handleCloseSheet} snapPoints={["60%", "85%"]}>
               <View style={styles.sheetContent}>
                  <Text style={styles.sheetTitle}>Выберите способы для входа</Text>
                  <View style={styles.authButtonsContainer}>
                     <AuthButton
                        onClick={() => {
                           navToAuthType("ClassicAuth");
                        }}
                        image={Facebook}
                        title="Войти с Facebook"
                     />
                     <AuthButton
                        onClick={() => {
                           navToAuthType("ClassicAuth");
                        }}
                        image={Google}
                        title="Войти с Google"
                     />
                     <AuthButton
                        onClick={() => {
                           navToAuthType("ClassicAuth");
                        }}
                        image={Apple}
                        title="Войти с Apple"
                     />
                     <AuthButton
                        onClick={() => {
                           navToAuthType("ClassicAuth");
                        }}
                        image={Email}
                        title="Войти с Email"
                     />
                  </View>
               </View>
            </CustomBottomSheet>
         </SafeAreaView>
      </GestureHandlerRootView>
   );
}

const styles = StyleSheet.create({
   flexContainer: {
      flex: 1,
   },
   container: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 16,
   },
   imageContainer: {
      justifyContent: "center",
      alignItems: "center",
      width: "100%",
   },
   image: {
      width: 300,
      height: 300,
      marginTop: 34,
      marginBottom: 51,
   },
   title: {
      fontWeight: "800",
      fontSize: 24,
      color: "#242B35",
      marginBottom: 22,
      textAlign: "center",
   },
   description: {
      fontWeight: "500",
      fontSize: 16,
      lineHeight: 19,
      textAlign: "center",
      color: "#8F8F91",
      width: 321,
      marginBottom: 46,
   },
   linkContainer: {
      marginTop: 24,
   },
   linkText: {
      fontWeight: "700",
      fontSize: 14,
      color: "#8F8F91",
      textAlign: "center",
   },
   sheetContent: {
      alignItems: "center",
      paddingHorizontal: 16,
   },
   sheetTitle: {
      fontSize: 20,
      fontWeight: "700",
      marginTop: 28,
      marginBottom: 12,
      textAlign: "center",
   },
   authButtonsContainer: {
      marginVertical: 12,
   },
});
