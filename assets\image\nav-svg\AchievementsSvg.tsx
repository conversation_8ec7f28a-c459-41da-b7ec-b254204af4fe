import { View } from "react-native";
import Svg, { Path, G } from "react-native-svg";

const AchievementsSvg = ({ color }: { color: string }) => (
   <View style={{ backgroundColor: color, borderRadius: 14, padding: 1, margin: "auto" }}>
      <Svg width="40" height="40" viewBox="0 0 52 56" fill="none">
         <Path
            opacity="0.5"
            d="M25.8347 36.0695C13.5849 36.0695 11.4148 23.8609 11.0304 14.1778C10.9235 11.4844 10.87 10.1376 11.8817 8.89155C12.8934 7.64552 14.1043 7.4412 16.5261 7.03259C18.9167 6.62926 22.0417 6.29517 25.8347 6.29517C29.6279 6.29517 32.753 6.62926 35.1434 7.03259C37.5651 7.4412 38.7761 7.64552 39.7878 8.89155C40.7995 10.1376 40.7461 11.4844 40.6391 14.1778C40.2548 23.8609 38.0847 36.0695 25.8347 36.0695Z"
            fill="#FFAA00"
         />
         <Path
            d="M37.8291 28.4602L43.8191 25.1323C45.4192 24.2433 46.2193 23.7988 46.6599 23.0499C47.1006 22.3011 47.1006 21.3858 47.1006 19.5553V19.401C47.1008 17.1815 47.1008 16.0718 46.4985 15.2363C45.8965 14.4008 44.8437 14.0499 42.738 13.348L40.7206 12.6755L40.6847 12.8555C40.6747 13.2581 40.6572 13.6965 40.6381 14.178C40.4503 18.9087 39.8363 24.2422 37.8291 28.4602Z"
            fill="#FFAA00"
         />
         <Path
            d="M11.029 14.178C11.2168 18.9089 11.8309 24.2426 13.8384 28.4608L7.84734 25.1323C6.24714 24.2433 5.44704 23.7988 5.0064 23.0499C4.56576 22.3011 4.56574 21.3858 4.56568 19.5553V19.401C4.56561 17.1815 4.56559 16.0718 5.16775 15.2363C5.76992 14.4008 6.8227 14.0499 8.92829 13.348L10.9458 12.6755L10.9826 12.8599C10.9926 13.2613 11.0099 13.6983 11.029 14.178Z"
            fill="#FFAA00"
         />
         <Path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M11.4778 48.8299C11.4778 47.949 12.1919 47.2349 13.0728 47.2349H38.5937C39.4746 47.2349 40.1887 47.949 40.1887 48.8299C40.1887 49.7108 39.4746 50.425 38.5937 50.425H13.0728C12.1919 50.425 11.4778 49.7108 11.4778 48.8299Z"
            fill="#FFAA00"
         />
         <Path
            opacity="0.5"
            d="M33.1878 47.235H18.481L19.1122 43.5136C19.311 42.5194 20.1839 41.804 21.1976 41.804H30.4711C31.4847 41.804 32.3578 42.5194 32.5564 43.5136L33.1878 47.235Z"
            fill="#FFAA00"
         />
         <Path
            d="M25.8338 36.07C25.2817 36.07 24.7502 36.0451 24.2383 35.9968V41.8039H27.4284V35.9968C26.9167 36.0451 26.3854 36.07 25.8338 36.07Z"
            fill="#FFAA00"
         />
         <Path
            d="M24.0169 14.8501C24.825 13.4004 25.2291 12.6755 25.8333 12.6755C26.4375 12.6755 26.8416 13.4004 27.6498 14.8501L27.8588 15.2252C28.0883 15.6372 28.2031 15.8432 28.3822 15.9791C28.5613 16.115 28.7842 16.1654 29.2302 16.2663L29.6361 16.3582C31.2055 16.7133 31.9902 16.8908 32.177 17.4911C32.3637 18.0915 31.8286 18.717 30.7588 19.9681L30.4819 20.2917C30.178 20.6472 30.026 20.825 29.9575 21.0449C29.8892 21.2648 29.9122 21.502 29.9581 21.9763L30 22.4082C30.1617 24.0774 30.2427 24.9119 29.754 25.283C29.265 25.6539 28.5305 25.3158 27.0611 24.6393L26.681 24.4642C26.2634 24.272 26.0547 24.1759 25.8333 24.1759C25.6119 24.1759 25.4033 24.272 24.9856 24.4642L24.6056 24.6393C23.1362 25.3158 22.4016 25.6539 21.9127 25.283C21.424 24.9119 21.5049 24.0774 21.6666 22.4082L21.7085 21.9763C21.7545 21.502 21.7774 21.2648 21.7092 21.0449C21.6407 20.825 21.4887 20.6472 21.1847 20.2917L20.9079 19.9681C19.838 18.717 19.3031 18.0915 19.4898 17.4911C19.6764 16.8908 20.4611 16.7133 22.0305 16.3582L22.4365 16.2663C22.8825 16.1654 23.1054 16.115 23.2844 15.9791C23.4635 15.8432 23.5783 15.6372 23.8078 15.2252L24.0169 14.8501Z"
            fill="#FFAA00"
         />
      </Svg>
   </View>
);

export default AchievementsSvg;
