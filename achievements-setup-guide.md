# Руководство по настройке системы достижений

## Быстрый старт

### 1. Применение миграций

Выполните миграции для обновления базы данных:

```bash
# Расширение таблиц достижений
migrate -path ./migrations -database "postgres://user:password@localhost/dbname?sslmode=disable" up

# Или через Docker
docker-compose exec app migrate -path ./migrations -database "**************************************/dbname?sslmode=disable" up
```

### 2. Проверка работы системы

После запуска приложения в логах должны появиться сообщения:

```
{"level":"INFO","message":"achievement service started"}
{"level":"INFO","message":"achievement notification callback set"}
```

### 3. Создание первого достижения

```bash
curl -X POST "http://localhost:4000/v1/admin/achievements/create" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Первый урок",
    "description": "Завершите первый урок",
    "type": "lessons",
    "target": 1,
    "icon": "🎯",
    "reward": 10,
    "category": "progress",
    "difficulty": "easy"
  }'
```

## Тестирование системы

### 1. Проверка автоматического обновления

Сохраните прогресс урока:

```bash
curl -X POST "http://localhost:4000/v1/progress/save" \
  -H "Authorization: Bearer USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 1,
    "module_id": 1,
    "mistaken_question_ids": [],
    "time": 120
  }'
```

### 2. Проверка достижений пользователя

```bash
curl -X GET "http://localhost:4000/v1/achievements/1" \
  -H "Authorization: Bearer USER_TOKEN"
```

### 3. Отметка изученных слов

```bash
curl -X POST "http://localhost:4000/v1/word/learned" \
  -H "Authorization: Bearer USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 1,
    "word_ids": [1, 2, 3]
  }'
```

## Предустановленные достижения

После применения миграций в системе будут доступны 42 предустановленных достижения:

### Достижения за уроки (6 штук)
- **Первые шаги** - 1 урок (10 очков)
- **Ученик** - 3 урока (25 очков)
- **Студент** - 7 уроков (50 очков)
- **Знаток** - 15 уроков (100 очков)
- **Мастер обучения** - 30 уроков (200 очков)
- **Легенда** - 50 уроков (500 очков)

### Достижения за слова (6 штук)
- **Словарный запас** - 5 слов (15 очков)
- **Полиглот** - 10 слов (30 очков)
- **Лингвист** - 25 слов (75 очков)
- **Мастер слов** - 50 слов (150 очков)
- **Словесный гений** - 100 слов (300 очков)
- **Казахский эрудит** - 200 слов (600 очков)

### Достижения за стрики (6 штук)
- **Постоянство** - 3 дня (20 очков)
- **Дисциплина** - 7 дней (50 очков)
- **Упорство** - 14 дней (100 очков)
- **Железная воля** - 30 дней (250 очков)
- **Несгибаемый** - 60 дней (500 очков)
- **Легенда постоянства** - 100 дней (1000 очков)

### И другие категории...

## Настройка уведомлений

### 1. Firebase настройка

Убедитесь, что файл `serviceAccount.json` находится в корне проекта и содержит корректные данные Firebase.

### 2. Регистрация device token

Пользователи должны зарегистрировать свои device tokens:

```bash
curl -X POST "http://localhost:4000/v1/notifications/device-token" \
  -H "Authorization: Bearer USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "firebase_device_token_here",
    "platform": "android"
  }'
```

### 3. Тестирование уведомлений

При получении достижения пользователь автоматически получит push-уведомление.

## Мониторинг и отладка

### 1. Логи системы

Следите за логами для отслеживания работы системы:

```bash
# Docker
docker-compose logs -f app | grep achievement

# Или напрямую
tail -f app.log | grep achievement
```

### 2. Проверка Redis кэша

```bash
# Подключение к Redis
redis-cli

# Проверка кэшированных достижений пользователя
GET user_achievements:1

# Просмотр всех ключей достижений
KEYS user_achievements:*
```

### 3. Проверка базы данных

```sql
-- Количество достижений в системе
SELECT COUNT(*) FROM achievements WHERE is_active = true;

-- Прогресс пользователя
SELECT 
    a.name,
    ua.progress,
    ua.achieved,
    a.target
FROM user_achievements ua
JOIN achievements a ON ua.achievement_id = a.id
WHERE ua.user_id = 1;

-- Статистика по категориям
SELECT 
    category,
    COUNT(*) as total,
    COUNT(CASE WHEN is_active THEN 1 END) as active
FROM achievements 
GROUP BY category;
```

## Частые проблемы и решения

### 1. События не обрабатываются

**Проблема**: Достижения не обновляются автоматически

**Решение**:
- Проверьте, что AchievementService запущен
- Убедитесь, что callback установлен
- Проверьте логи на ошибки

### 2. Уведомления не приходят

**Проблема**: Push-уведомления не отправляются

**Решение**:
- Проверьте Firebase конфигурацию
- Убедитесь, что device token зарегистрирован
- Проверьте права приложения на отправку уведомлений

### 3. Медленная работа

**Проблема**: Долгое получение достижений

**Решение**:
- Проверьте работу Redis кэша
- Убедитесь, что индексы созданы
- Оптимизируйте запросы

### 4. Дублирование достижений

**Проблема**: Пользователь получает одно достижение несколько раз

**Решение**:
- Проверьте уникальный индекс на (user_id, achievement_id)
- Убедитесь в корректности логики обновления

## Расширение системы

### 1. Добавление нового типа достижения

1. Добавьте константу в `internal/data/achievements.go`:
```go
const AchievementTypeNewType = "new_type"
```

2. Обновите валидацию в `ValidateAchievement()`

3. Добавьте обработчик в `AchievementService`

4. Создайте соответствующие достижения через API

### 2. Добавление новой категории

1. Добавьте константу категории
2. Обновите валидацию
3. Добавьте обработку в уведомлениях
4. Создайте достижения новой категории

### 3. Интеграция с новыми эндпоинтами

Для интеграции с новыми действиями пользователя:

```go
// В обработчике нового действия
go func() {
    event := data.CreateAchievementEvent(userID, "new_event_type", map[string]interface{}{
        "custom_data": value,
    })
    app.models.AchievementService.SendEvent(event)
}()
```

## Производительность

### Рекомендации по оптимизации:

1. **Используйте кэш** - всегда получайте достижения через `GetUserAchievementsWithCache()`

2. **Batch операции** - группируйте обновления достижений

3. **Асинхронность** - отправляйте события в горутинах

4. **Мониторинг** - следите за метриками производительности

5. **Индексы** - убедитесь, что все необходимые индексы созданы

## Безопасность

### Рекомендации:

1. **Валидация входных данных** - всегда проверяйте данные от клиента

2. **Права доступа** - используйте middleware для проверки прав

3. **Rate limiting** - ограничивайте частоту запросов

4. **Логирование** - ведите аудит всех действий с достижениями

5. **Санитизация** - очищайте пользовательские данные

## Поддержка

При возникновении проблем:

1. Проверьте логи приложения
2. Убедитесь в корректности конфигурации
3. Проверьте состояние зависимых сервисов (Redis, PostgreSQL, Firebase)
4. Обратитесь к документации API
5. Создайте issue с подробным описанием проблемы
