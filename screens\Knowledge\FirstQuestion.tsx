import { useState } from "react";
import { View } from "react-native";
import ChoiceButton from "../../components/ui/ChoiceButton";
import { useDispatch } from "react-redux";
import { setValid } from "../../store/slices/buttonSlice";

export default function FirstQuestion() {
   const mockLevels = [
      {
         title: "Начинающий",
         level: 1,
      },
      {
         title: "Знаю несколько слов",
         level: 2,
      },
      {
         title: "Могу читать и разговаривать",
         level: 3,
      },
   ];

   const [selectedLevel, setSelectedLevel] = useState<number | null>(null);
   const dispatch = useDispatch();

   const handleLevelSelect = (level: number) => {
      setSelectedLevel(level);
      dispatch(setValid(true));
   };
   return (
      <View>
         <View style={{ height: 220, justifyContent: "space-between" }}>
            {mockLevels.map((item, index) => {
               return (
                  <ChoiceButton
                     key={index}
                     level={item.level}
                     handlePress={() => handleLevelSelect(item.level)}
                     title={item.title}
                     isPicked={selectedLevel === item.level}
                  />
               );
            })}
         </View>
      </View>
   );
}
