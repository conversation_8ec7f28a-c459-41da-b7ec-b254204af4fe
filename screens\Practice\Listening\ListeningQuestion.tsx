import React, { useState, useRef, useEffect } from "react";
import {
   StyleSheet,
   Text,
   TouchableOpacity,
   View,
   Animated,
} from "react-native";
import AudioLargeSvg from "../../../assets/svg/AudioLargeSvg";
import VariantButton from "../../../components/ui/VariantButton";
import ChoiceRequiredButton from "../../../components/ui/ChoiceRequiredButton";
import { Audio } from "expo-av";
import ReportSvg from "../../../assets/svg/ReportSvg";
import AudioSvg from "../../../assets/svg/AudioSvg";
import { PlayCorrectSound, PlayWrongSound } from "../../../utils/PlaySound";

export function ListeningQuestion({
   question,
   onNextQuestion,
   onSaveResult,
}: {
   question: any;
   onNextQuestion: () => void;
   onSaveResult: (isCorrect: boolean) => void;
}) {
   const [selectedAnswer, setSelectedAnswer] = useState(0);
   const [showCorrectAnswer, setShowCorrectAnswer] = useState(false);
   const [showWrongAnswer, setShowWrongAnswer] = useState(false);

   const reply = async () => {
      if (question.correct_answer.id === selectedAnswer) {
         PlayCorrectSound();
         setShowCorrectAnswer(true);
         onSaveResult(true); // Сохранение результата
      } else {
         PlayWrongSound();
         setShowWrongAnswer(true);
         onSaveResult(false); // Сохранение результата
      }
   };

   const handleNext = () => {
      setShowCorrectAnswer(false);
      setShowWrongAnswer(false);
      setSelectedAnswer(0);
      onNextQuestion(); // Переход к следующему вопросу
   };

   const [isPlaying, setIsPlaying] = useState(false);
   const scaleAnim = useRef(new Animated.Value(1)).current;
   const opacityAnim = useRef(new Animated.Value(0)).current;

   const playSound = async () => {
      const { sound } = await Audio.Sound.createAsync(
         question.correct_answer.url
      );
      await sound.playAsync();

      setIsPlaying(true);
      // Анимация
      Animated.timing(scaleAnim, {
         toValue: 1.15, // Увеличение размера
         duration: 300, // Длительность анимации
         useNativeDriver: true,
      }).start();

      Animated.timing(opacityAnim, {
         toValue: 0.2, // Понижение прозрачности
         duration: 300,
         useNativeDriver: true,
      }).start();

      setTimeout(() => {
         // Возвращаем анимацию к исходному состоянию
         Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
         }).start();

         Animated.timing(opacityAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
         }).start();
      }, 500);

      sound.setOnPlaybackStatusUpdate((status) => {
         // @ts-ignore
         if (status.didJustFinish) {
            sound.unloadAsync();
            setIsPlaying(false);
         }
      });
   };

   return (
      <View
         style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "space-between",
            height: "100%",
            paddingTop: 55,
            paddingBottom: 80,
         }}
      >
         <Text
            style={{
               fontWeight: 600,
               fontSize: 20,
               color: "#242B35",
               marginHorizontal: "auto",
            }}
         >
            Выберите перевод
         </Text>

         <View style={styles.audioContainer}>
            {/* Анимированный задний блок */}
            <Animated.View
               style={[
                  styles.animatedBackground,
                  {
                     transform: [{ scale: scaleAnim }],
                     opacity: opacityAnim,
                  },
               ]}
            />
            {/* Кнопка с иконкой аудио */}
            <TouchableOpacity onPress={playSound} style={styles.audioButton}>
               <AudioLargeSvg />
            </TouchableOpacity>
         </View>

         {/* Variants */}
         <View>
            {question.variants.map((word: any) => (
               <VariantButton
                  key={word.id}
                  isPicked={selectedAnswer === word.id}
                  title={word.plaintext}
                  handlePress={() => setSelectedAnswer(word.id)}
               />
            ))}

            <View style={{ marginTop: 10 }}>
               <ChoiceRequiredButton
                  isValid={selectedAnswer !== 0}
                  title="Проверить"
                  handlePress={reply}
               />
            </View>
         </View>

         {showCorrectAnswer && (
            <CorrectAnswerBlock onNext={handleNext} question={question} />
         )}
         {showWrongAnswer && (
            <WrongAnswerBlock onNext={handleNext} question={question} />
         )}
      </View>
   );
}

const CorrectAnswerBlock = ({
   onNext,
   question,
}: {
   onNext: () => void;
   question: any;
}) => {
   const translateYAnim = useRef(new Animated.Value(220)).current;

   useEffect(() => {
      Animated.timing(translateYAnim, {
         toValue: 0,
         duration: 150,
         useNativeDriver: true,
      }).start();
   }, []);
   const playSound = async () => {
      const { sound } = await Audio.Sound.createAsync(
         question.correct_answer.url
      );
      await sound.playAsync();

      sound.setOnPlaybackStatusUpdate((status) => {
         // @ts-ignore
         if (status.didJustFinish) {
            sound.unloadAsync();
         }
      });
   };

   return (
      <Animated.View
         style={[
            {
               backgroundColor: "#cceff4",
               width: "100%",
               height: 220,
               position: "absolute",
               borderRadius: 15,
               bottom: 0,
               padding: 20,
               transform: [{ translateY: translateYAnim }],
            },
         ]}
      >
         <View
            style={{
               marginHorizontal: "auto",
               width: "90%",
               display: "flex",
               flexDirection: "row",
               justifyContent: "space-between",
               alignItems: "center",
            }}
         >
            <Text
               style={{
                  color: "#242B35",
                  fontWeight: "700",
                  fontSize: 20,
               }}
            >
               Верно
            </Text>

            <View
               style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
               }}
            >
               <View style={{ marginRight: 5 }}>
                  <ReportSvg />
               </View>
               <TouchableOpacity onPress={playSound}>
                  <AudioSvg />
               </TouchableOpacity>
            </View>
         </View>

         <View style={{ marginTop: 45 }}>
            <ChoiceRequiredButton
               isValid={true}
               title="Далее"
               handlePress={onNext}
            />
         </View>
      </Animated.View>
   );
};

const WrongAnswerBlock = ({
   onNext,
   question,
}: {
   onNext: () => void;
   question: any;
}) => {
   const translateYAnim = useRef(new Animated.Value(220)).current;

   useEffect(() => {
      Animated.timing(translateYAnim, {
         toValue: 0,
         duration: 150,
         useNativeDriver: true,
      }).start();
   }, []);

   const playSound = async () => {
      const { sound } = await Audio.Sound.createAsync(
         question.correct_answer.url
      );
      await sound.playAsync();

      sound.setOnPlaybackStatusUpdate((status) => {
         // @ts-ignore
         if (status.didJustFinish) {
            sound.unloadAsync();
         }
      });
   };

   return (
      <Animated.View
         style={[
            {
               backgroundColor: "#fbd4ce",
               width: "100%",
               height: 220,
               position: "absolute",
               borderRadius: 15,
               bottom: 0,
               padding: 20,
               transform: [{ translateY: translateYAnim }],
            },
         ]}
      >
         <View
            style={{
               marginHorizontal: "auto",
               width: "90%",
               display: "flex",
               flexDirection: "row",
               justifyContent: "space-between",
               alignItems: "center",
            }}
         >
            <Text
               style={{
                  color: "#ED2B08",
                  fontWeight: "700",
                  fontSize: 20,
               }}
            >
               Правильный ответ
            </Text>

            <View
               style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
               }}
            >
               <View style={{ marginRight: 5 }}>
                  <ReportSvg color="#ED2B08" />
               </View>
               <TouchableOpacity onPress={playSound}>
                  <AudioSvg color="#ED2B08" />
               </TouchableOpacity>
            </View>
         </View>

         <Text
            style={{
               color: "#ED2B08",
               fontWeight: "500",
               fontSize: 12,
               marginTop: 14,
               marginHorizontal: "auto",
               width: "90%",
               display: "flex",
               flexDirection: "row",
               justifyContent: "space-between",
            }}
         >
            {question.correct_answer.plaintext}
         </Text>

         <View style={{ marginTop: 45 }}>
            <ChoiceRequiredButton
               color="#ED2B08"
               borderColor="#BD2106"
               isValid={true}
               title="Далее"
               handlePress={onNext}
            />
         </View>
      </Animated.View>
   );
};

const styles = StyleSheet.create({
   audioContainer: {
      width: 200,
      height: 200,
      borderRadius: 55,
      backgroundColor: "#00AFCA40",
      justifyContent: "center",
      alignItems: "center",
      marginHorizontal: "auto",
      position: "relative",
   },
   animatedBackground: {
      position: "absolute",
      width: 200,
      height: 200,
      borderRadius: 55,
      backgroundColor: "#00AFCA",
      zIndex: -1,
   },
   audioButton: {
      margin: "auto",
   },
   answerBlock: {
      width: "100%",
      height: 220,
      position: "absolute",
      bottom: 0,
      padding: 20,
      borderRadius: 15,
   },
   answerHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
   },
   correctText: {
      color: "#242B35",
      fontWeight: "700",
      fontSize: 20,
   },
   wrongText: {
      color: "#ED2B08",
      fontWeight: "700",
      fontSize: 20,
   },
});
