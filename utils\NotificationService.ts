import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Условный импорт Firebase (только для Development Build)
let messaging: any = null;
try {
  messaging = require('@react-native-firebase/messaging').default;
} catch (error) {
  console.log('Firebase not available, using Expo Push Tokens only');
}

// Типы для уведомлений
export interface DeviceToken {
  id: number;
  user_id: number;
  token: string;
  device_type: 'ios' | 'android' | 'web';
  device_id?: string;
  device_name?: string;
  app_version?: string;
  is_active: boolean;
  last_used_at: string;
  created_at: string;
  updated_at: string;
}

export interface NotificationSettings {
  id: number;
  user_id: number;
  push_enabled: boolean;
  achievements_enabled: boolean;
  reminders_enabled: boolean;
  new_content_enabled: boolean;
  daily_reminder_time: string;
  timezone: string;
  created_at: string;
  updated_at: string;
}

export interface NotificationHistory {
  id: number;
  user_id: number;
  type: string;
  title: string;
  body: string;
  data?: any;
  sent_at: string;
  delivered_at?: string;
  opened_at?: string;
  status: 'sent' | 'delivered' | 'failed' | 'opened';
}

// Конфигурация уведомлений
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

class NotificationService {
  private static instance: NotificationService;
  private pushToken: string | null = null;
  private tokenType: 'fcm' | 'expo' | null = null;
  private notificationListener: any = null;
  private responseListener: any = null;

  private constructor() {}

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Инициализация сервиса уведомлений
   * Поддерживает как FCM, так и Expo токены
   */
  public async initialize(): Promise<void> {
    try {
      // Проверяем, что это физическое устройство
      if (!Device.isDevice) {
        console.warn('Push notifications only work on physical devices');
        return;
      }

      // Пытаемся получить FCM токен (для Development Build)
      if (messaging) {
        try {
          const authStatus = await messaging().requestPermission();
          const enabled =
            authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
            authStatus === messaging.AuthorizationStatus.PROVISIONAL;

          if (enabled) {
            const fcmToken = await messaging().getToken();
            if (fcmToken) {
              this.pushToken = fcmToken;
              this.tokenType = 'fcm';
              console.log('FCM Token:', this.pushToken);

              // Сохраняем токен локально
              await AsyncStorage.setItem('pushToken', this.pushToken!);
              await AsyncStorage.setItem('tokenType', this.tokenType!);

              // Настраиваем слушатели FCM
              this.setupFCMListeners();
              this.setupNotificationListeners();
              return;
            }
          }
        } catch (fcmError) {
          console.log('FCM error:', fcmError);
        }
      } else {
        console.log('Firebase not available, using Expo Push Tokens');
      }

      // Fallback к Expo Push Tokens (для Expo Go)
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Failed to get push notification permissions!');
        return;
      }

      // Получаем Expo Push Token
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: '44381267-7d76-47fa-ad7d-012514f982e2',
      });

      this.pushToken = token.data;
      this.tokenType = 'expo';
      console.log('Expo Push Token:', this.pushToken);

      // Сохраняем токен локально
      await AsyncStorage.setItem('pushToken', this.pushToken);
      await AsyncStorage.setItem('tokenType', this.tokenType);

      // Настраиваем слушатели
      this.setupNotificationListeners();

    } catch (error) {
      console.error('Error initializing notifications:', error);
    }
  }



  /**
   * Настройка слушателей FCM
   */
  private setupFCMListeners(): void {
    if (!messaging) {
      console.log('Firebase messaging not available');
      return;
    }

    try {
      // Слушатель для уведомлений в фоне
      messaging().setBackgroundMessageHandler(async (remoteMessage: any) => {
        console.log('Message handled in the background!', remoteMessage);
      });

      // Слушатель для уведомлений когда приложение в фокусе
      messaging().onMessage(async (remoteMessage: any) => {
        console.log('A new FCM message arrived!', remoteMessage);

        // Показываем локальное уведомление
        if (remoteMessage.notification) {
          await Notifications.scheduleNotificationAsync({
            content: {
              title: remoteMessage.notification.title || 'Уведомление',
              body: remoteMessage.notification.body || '',
              data: remoteMessage.data || {},
            },
            trigger: null,
          });
        }
      });

      // Слушатель для открытия уведомления
      messaging().onNotificationOpenedApp((remoteMessage: any) => {
        console.log('Notification caused app to open from background state:', remoteMessage);
        this.handleFCMNotificationResponse(remoteMessage);
      });

      // Проверяем, было ли приложение открыто из уведомления
      messaging()
        .getInitialNotification()
        .then((remoteMessage: any) => {
          if (remoteMessage) {
            console.log('Notification caused app to open from quit state:', remoteMessage);
            this.handleFCMNotificationResponse(remoteMessage);
          }
        });
    } catch (error) {
      console.error('Error setting up FCM listeners:', error);
    }
  }

  /**
   * Настройка слушателей уведомлений (для локальных уведомлений)
   */
  private setupNotificationListeners(): void {
    // Слушатель входящих уведомлений
    this.notificationListener = Notifications.addNotificationReceivedListener(notification => {
      console.log('Local notification received:', notification);
    });

    // Слушатель ответов на уведомления (когда пользователь нажимает на уведомление)
    this.responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Local notification response:', response);
      this.handleNotificationResponse(response);
    });
  }

  /**
   * Обработка ответа на FCM уведомление
   */
  private handleFCMNotificationResponse(remoteMessage: any): void {
    const data = remoteMessage.data;

    // Обработка различных типов уведомлений
    if (data?.type) {
      switch (data.type) {
        case 'achievement':
          console.log('Navigate to achievements');
          break;
        case 'reminder':
          console.log('Navigate to lessons');
          break;
        case 'new_content':
          console.log('Navigate to new content');
          break;
        default:
          console.log('Unknown notification type:', data.type);
      }
    }
  }

  /**
   * Обработка ответа на локальное уведомление
   */
  private handleNotificationResponse(response: Notifications.NotificationResponse): void {
    const { notification } = response;
    const data = notification.request.content.data;

    // Обработка различных типов уведомлений
    if (data?.type) {
      switch (data.type) {
        case 'achievement':
          console.log('Navigate to achievements');
          break;
        case 'reminder':
          console.log('Navigate to lessons');
          break;
        case 'new_content':
          console.log('Navigate to new content');
          break;
        default:
          console.log('Unknown notification type:', data.type);
      }
    }
  }

  /**
   * Получение текущего Push токена
   */
  public getToken(): string | null {
    return this.pushToken;
  }

  /**
   * Получение типа токена
   */
  public getTokenType(): 'fcm' | 'expo' | null {
    return this.tokenType;
  }

  /**
   * Получение информации об устройстве
   */
  public async getDeviceInfo(): Promise<{
    device_type: 'ios' | 'android' | 'web';
    device_id?: string;
    device_name?: string;
    app_version?: string;
  }> {
    const deviceType = Platform.OS as 'ios' | 'android' | 'web';
    
    return {
      device_type: deviceType,
      device_id: await Device.getDeviceTypeAsync().then(type => Device.DeviceType[type]),
      device_name: Device.deviceName || `${Platform.OS} Device`,
      app_version: '1.0.0', // Можно получить из package.json или Constants
    };
  }

  /**
   * Проверка статуса разрешений
   */
  public async getPermissionStatus(): Promise<Notifications.PermissionStatus> {
    const { status } = await Notifications.getPermissionsAsync();
    return status;
  }

  /**
   * Запрос разрешений
   */
  public async requestPermissions(): Promise<boolean> {
    const { status } = await Notifications.requestPermissionsAsync();
    return status === 'granted';
  }

  /**
   * Отправка локального уведомления (для тестирования)
   */
  public async sendLocalNotification(title: string, body: string, data?: any): Promise<void> {
    await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
      },
      trigger: null, // Отправить немедленно
    });
  }

  /**
   * Очистка слушателей при размонтировании
   */
  public cleanup(): void {
    if (this.notificationListener) {
      this.notificationListener.remove();
    }
    if (this.responseListener) {
      this.responseListener.remove();
    }
  }

  /**
   * Получение количества непрочитанных уведомлений
   */
  public async getBadgeCount(): Promise<number> {
    return await Notifications.getBadgeCountAsync();
  }

  /**
   * Установка количества непрочитанных уведомлений
   */
  public async setBadgeCount(count: number): Promise<void> {
    await Notifications.setBadgeCountAsync(count);
  }

  /**
   * Очистка всех уведомлений
   */
  public async clearAllNotifications(): Promise<void> {
    await Notifications.dismissAllNotificationsAsync();
  }
}

export default NotificationService.getInstance();
