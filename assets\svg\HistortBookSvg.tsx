import Svg, { <PERSON>, <PERSON>, <PERSON>lip<PERSON><PERSON>, Defs, Rect } from "react-native-svg";

const HistoryBookSvg = () => (
   <Svg width="77" height="77" viewBox="0 0 77 77" fill="none">
      <Path
         opacity="0.5"
         d="M9.625 25.6667C9.625 16.5922 9.625 12.0549 12.4441 9.23585C15.2632 6.41675 19.8005 6.41675 28.875 6.41675H48.125C57.1995 6.41675 61.7368 6.41675 64.5559 9.23585C67.375 12.0549 67.375 16.5922 67.375 25.6667V51.3334C67.375 60.408 67.375 64.9452 64.5559 67.7643C61.7368 70.5834 57.1995 70.5834 48.125 70.5834H28.875C19.8005 70.5834 15.2632 70.5834 12.4441 67.7643C9.625 64.9452 9.625 60.408 9.625 51.3334V25.6667Z"
         fill="#F7AD3A"
      />
      <Path
         fillRule="evenodd"
         clipRule="evenodd"
         d="M28.073 6.45654V70.6196H23.2605V6.45654H28.073Z"
         fill="#F7AD3A"
      />
      <Path
         fillRule="evenodd"
         clipRule="evenodd"
         d="M4.0105 25.6667C4.0105 24.3378 5.08781 23.2605 6.41675 23.2605H12.8334C14.1623 23.2605 15.2397 24.3378 15.2397 25.6667C15.2397 26.9957 14.1623 28.073 12.8334 28.073H6.41675C5.08781 28.073 4.0105 26.9957 4.0105 25.6667ZM4.0105 38.5001C4.0105 37.1711 5.08781 36.0938 6.41675 36.0938H12.8334C14.1623 36.0938 15.2397 37.1711 15.2397 38.5001C15.2397 39.829 14.1623 40.9063 12.8334 40.9063H6.41675C5.08781 40.9063 4.0105 39.829 4.0105 38.5001ZM4.0105 51.3334C4.0105 50.0045 5.08781 48.9272 6.41675 48.9272H12.8334C14.1623 48.9272 15.2397 50.0045 15.2397 51.3334C15.2397 52.6623 14.1623 53.7397 12.8334 53.7397H6.41675C5.08781 53.7397 4.0105 52.6623 4.0105 51.3334Z"
         fill="#F7AD3A"
      />
      <Path
         d="M34.4895 20.8542C34.4895 19.5253 35.5668 18.448 36.8958 18.448H52.9374C54.2664 18.448 55.3437 19.5253 55.3437 20.8542C55.3437 22.1832 54.2664 23.2605 52.9374 23.2605H36.8958C35.5668 23.2605 34.4895 22.1832 34.4895 20.8542Z"
         fill="#F7AD3A"
      />
      <Path
         d="M34.4895 32.0834C34.4895 30.7545 35.5668 29.6772 36.8958 29.6772H52.9374C54.2664 29.6772 55.3437 30.7545 55.3437 32.0834C55.3437 33.4123 54.2664 34.4897 52.9374 34.4897H36.8958C35.5668 34.4897 34.4895 33.4123 34.4895 32.0834Z"
         fill="#F7AD3A"
      />
   </Svg>
);

export default HistoryBookSvg;
