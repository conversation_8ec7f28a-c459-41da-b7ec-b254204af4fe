import { configureStore, createSlice, PayloadAction } from '@reduxjs/toolkit';

// Создание среза (slice) для управления валидностью кнопки
const buttonSlice = createSlice({
  name: 'button',
  initialState: {
    isValid: false,
  },
  reducers: {
    setValid: (state, action: PayloadAction<boolean>) => {
      state.isValid = action.payload;
    },
  },
});

// Экспортируем действия
export const { setValid } = buttonSlice.actions;

export default buttonSlice.reducer