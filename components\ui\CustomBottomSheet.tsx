import React, { useCallback, useMemo, forwardRef } from "react";
import {
   BottomSheetModal,
   BottomSheetView,
   BottomSheetBackdrop,
   BottomSheetModalProvider,
} from "@gorhom/bottom-sheet";
import { StyleSheet } from "react-native";

interface CustomBottomSheetProps {
   snapPoints?: string[] | number[];
   initialIndex?: number;
   onChange?: (index: number) => void;
   children?: React.ReactNode;
   onClose: () => void;
}

const CustomBottomSheet = forwardRef<BottomSheetModal, CustomBottomSheetProps>(
   (
      {
         snapPoints = ["25%", "50%"],
         initialIndex = 0,
         onChange,
         children,
         onClose,
      },
      ref
   ) => {
      const memoizedSnapPoints = useMemo(() => snapPoints, [snapPoints]);

      const handleSheetChanges = useCallback(
         (index: number) => {
            if (onChange) {
               onChange(index);
            }
         },
         [onChange]
      );

      return (
         <BottomSheetModalProvider>
            <BottomSheetModal
               ref={ref}
               index={initialIndex}
               snapPoints={memoizedSnapPoints}
               onChange={handleSheetChanges}
               backdropComponent={(props) => (
                  <BottomSheetBackdrop
                     {...props}
                     disappearsOnIndex={-1}
                     appearsOnIndex={0}
                  />
               )}
               onDismiss={onClose}
            >
               <BottomSheetView style={styles.contentContainer}>
                  {children}
               </BottomSheetView>
            </BottomSheetModal>
         </BottomSheetModalProvider>
      );
   }
);

const styles = StyleSheet.create({
   contentContainer: {
      flex: 1,
      //   alignItems: "center",
      //   justifyContent: "center",
      //   padding: 20,
   },
});

export default CustomBottomSheet;
