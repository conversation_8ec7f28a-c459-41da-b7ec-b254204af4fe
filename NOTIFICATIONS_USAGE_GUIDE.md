# 📱 Руководство по использованию Push-уведомлений

## 🚀 Быстрый старт

### 1. Запуск системы
```bash
# Запуск Docker контейнеров
docker-compose up -d

# Проверка статуса
docker-compose ps
```

### 2. Тестирование через браузер
```bash
# Запуск HTTP сервера для админки
go run serve_html.go

# Откройте в браузере:
# http://localhost:3000/admin_notifications.html
```

### 3. Получение JWT токена
```bash
# Авторизация пользователя
curl -X POST http://localhost:8080/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your_password"
  }'

# Скопируйте access_token из ответа
```

## 📋 Доступные методы

### 👤 Индивидуальные уведомления
```bash
curl -X POST http://localhost:8080/v1/notifications/send \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 1,
    "type": "achievement",
    "title": "🎉 Новое достижение!",
    "body": "Поздравляем с первым уроком!"
  }'
```

### 📢 Массовые уведомления
```bash
curl -X POST http://localhost:8080/v1/notifications/bulk \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_ids": [1, 2, 3],
    "type": "new_content",
    "title": "📚 Новый урок!",
    "body": "Изучите новый урок по грамматике"
  }'
```

### 👥 Групповые уведомления
```bash
curl -X POST http://localhost:8080/v1/notifications/group \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "group_type": "all",
    "type": "announcement",
    "title": "📢 Объявление",
    "body": "Новая версия приложения доступна!"
  }'
```

### 🛡️ Админские уведомления
```bash
curl -X POST http://localhost:8080/v1/notifications/admin \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "target_type": "all",
    "type": "announcement",
    "title": "📢 Важное объявление",
    "body": "Техническое обслуживание сегодня в 02:00"
  }'
```

## 🔧 Настройка токенов

### Expo Go (разработка)
1. Установите Expo CLI: `npm install -g @expo/cli`
2. В приложении получите токен:
```javascript
import * as Notifications from 'expo-notifications';

const token = await Notifications.getExpoPushTokenAsync();
console.log('Token:', token.data);
```

3. Зарегистрируйте токен:
```bash
curl -X POST http://localhost:8080/v1/notifications/register \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "ExponentPushToken[6tOERJIM_Lx_mSrJg-ojdJ]",
    "device_type": "ios",
    "device_name": "iPhone 15"
  }'
```

### Standalone приложения (продакшн)
```javascript
// В React Native приложении
import * as Notifications from 'expo-notifications';

const token = await Notifications.getExpoPushTokenAsync({
  projectId: 'your-expo-project-id'
});

// Отправьте token.data на сервер
```

## 📊 Мониторинг

### Просмотр логов
```bash
# Логи приложения
docker-compose logs app -f

# Логи в MongoDB
# Подключитесь к MongoDB и выполните:
db.logs.find({"properties.service": {$exists: true}}).sort({time: -1})
```

### Метрики
```bash
# Системные метрики
curl http://localhost:8080/debug/vars

# История уведомлений пользователя
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/v1/notifications/history
```

## 🎯 Типы уведомлений

| Тип | Описание | Пример использования |
|-----|----------|---------------------|
| `achievement` | Достижения | Завершение урока, получение награды |
| `reminder` | Напоминания | Ежедневная практика, пропущенные уроки |
| `new_content` | Новый контент | Новые уроки, обновления курса |
| `progress` | Прогресс | Статистика обучения, достижения целей |
| `welcome` | Приветствие | Регистрация, первый вход |
| `announcement` | Объявления | Новости, обновления системы |

## 🔐 Права доступа

### Обычные пользователи
- ✅ Отправка индивидуальных уведомлений
- ✅ Отправка массовых уведомлений (до 1000 пользователей)
- ✅ Просмотр своей истории уведомлений
- ✅ Управление настройками уведомлений

### Администраторы
- ✅ Все права обычных пользователей
- ✅ Отправка групповых уведомлений
- ✅ Отправка админских уведомлений всем пользователям
- ✅ Просмотр статистики системы
- ✅ Управление настройками других пользователей

## 🚨 Лимиты и ограничения

### Rate Limiting
- **Обычные пользователи**: 100 запросов/минуту
- **Администраторы**: 1000 запросов/минуту

### Размеры
- **Заголовок**: до 100 символов
- **Текст**: до 500 символов
- **Payload**: до 4KB
- **Массовая отправка**: до 1000 пользователей

### Отложенная отправка
- **Максимальный срок**: 30 дней в будущем
- **Минимальный интервал**: 1 минута

## 🛠️ Устранение неполадок

### Уведомления не приходят
1. Проверьте токен устройства
2. Убедитесь, что уведомления включены в настройках
3. Проверьте логи сервера
4. Убедитесь, что Expo Go запущено (для разработки)

### Ошибки API
```bash
# Проверка статуса сервера
curl http://localhost:8080/v1/healthcheck

# Проверка токена
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/v1/notifications/settings
```

### Проблемы с Docker
```bash
# Перезапуск сервисов
docker-compose restart app

# Просмотр логов
docker-compose logs app --tail=50

# Пересборка образа
docker-compose build app
```

## 📈 Оптимизация производительности

### Массовые рассылки
- Используйте bulk endpoints для отправки нескольким пользователям
- Разбивайте большие списки на батчи по 1000 пользователей
- Используйте отложенную отправку для равномерной нагрузки

### Мониторинг
- Настройте алерты на высокий процент ошибок
- Мониторьте время ответа API
- Отслеживайте использование RabbitMQ очередей

## 🎉 Готово!

Ваша система push-уведомлений готова к использованию! 

### Следующие шаги:
1. 🔧 Настройте продакшн домены в CORS
2. 🔐 Создайте админские аккаунты
3. 📱 Интегрируйте с мобильным приложением
4. 📊 Настройте мониторинг и алерты
5. 🚀 Запустите в продакшн!

Удачи! 🚀
