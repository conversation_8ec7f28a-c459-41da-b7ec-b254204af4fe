import { useCallback } from 'react';
import { 
  markWordsAsLearned, 
  markQuestionAnswered, 
  saveProgress,
  updateAchievementProgress 
} from '../utils/service';
import { useAuth } from '../context/AuthContext';

export const useAchievementActions = () => {
  const { authState } = useAuth();

  /**
   * Отметить слова как изученные и обновить достижения
   */
  const markWordsLearned = useCallback(async (wordIds: number[]) => {
    if (!authState.user?.id) return;

    try {
      await markWordsAsLearned({
        user_id: authState.user.id,
        word_ids: wordIds,
      });
      console.log(`Marked ${wordIds.length} words as learned`);
    } catch (error) {
      console.error('Error marking words as learned:', error);
    }
  }, [authState.user?.id]);

  /**
   * Отметить ответ на вопрос и обновить достижения
   */
  const markQuestionAnswer = useCallback(async (questionId: number, isCorrect: boolean) => {
    if (!authState.user?.id) return;

    try {
      await markQuestionAnswered({
        user_id: authState.user.id,
        question_id: questionId,
        is_correct: isCorrect,
      });
      console.log(`Question ${questionId} answered: ${isCorrect ? 'correct' : 'incorrect'}`);
    } catch (error) {
      console.error('Error marking question as answered:', error);
    }
  }, [authState.user?.id]);

  /**
   * Сохранить прогресс урока и обновить достижения
   */
  const saveLessonProgress = useCallback(async (
    moduleId: number, 
    mistakenQuestionIds: number[], 
    timeSpent: number
  ) => {
    if (!authState.user?.id) return;

    try {
      await saveProgress({
        user_id: authState.user.id,
        module_id: moduleId,
        mistaken_question_ids: mistakenQuestionIds,
        time: timeSpent,
      });
      console.log(`Progress saved for module ${moduleId}`);
    } catch (error) {
      console.error('Error saving lesson progress:', error);
    }
  }, [authState.user?.id]);

  /**
   * Обновить прогресс конкретного достижения
   */
  const updateSpecificAchievement = useCallback(async (
    achievementId: number, 
    progress: number
  ) => {
    if (!authState.user?.id) return;

    try {
      const result = await updateAchievementProgress({
        user_id: authState.user.id,
        achievement_id: achievementId,
        progress: progress,
      });
      
      if (result.result.newly_achieved) {
        console.log(`Achievement unlocked: ${result.result.achievement_name}`);
      }
      
      return result;
    } catch (error) {
      console.error('Error updating achievement progress:', error);
    }
  }, [authState.user?.id]);

  /**
   * Симуляция действий для тестирования (можно удалить в продакшене)
   */
  const simulateActions = {
    completeLesson: () => saveLessonProgress(1, [], 120),
    learnWords: () => markWordsLearned([1, 2, 3]),
    answerQuestions: () => {
      markQuestionAnswer(1, true);
      markQuestionAnswer(2, true);
      markQuestionAnswer(3, false);
      markQuestionAnswer(4, true);
      markQuestionAnswer(5, true);
    },
  };

  return {
    markWordsLearned,
    markQuestionAnswer,
    saveLessonProgress,
    updateSpecificAchievement,
    simulateActions, // Для тестирования
  };
};

// Типы для интеграции с компонентами уроков
export interface LessonCompletionData {
  moduleId: number;
  mistakenQuestionIds: number[];
  timeSpent: number;
  wordsLearned?: number[];
  questionsAnswered?: Array<{
    questionId: number;
    isCorrect: boolean;
  }>;
}

/**
 * Хук для автоматической обработки завершения урока
 */
export const useLessonCompletion = () => {
  const { markWordsLearned, markQuestionAnswer, saveLessonProgress } = useAchievementActions();

  const completeLessonWithAchievements = useCallback(async (data: LessonCompletionData) => {
    try {
      // Сохранить основной прогресс урока
      await saveLessonProgress(data.moduleId, data.mistakenQuestionIds, data.timeSpent);

      // Отметить изученные слова
      if (data.wordsLearned && data.wordsLearned.length > 0) {
        await markWordsLearned(data.wordsLearned);
      }

      // Отметить ответы на вопросы
      if (data.questionsAnswered && data.questionsAnswered.length > 0) {
        for (const answer of data.questionsAnswered) {
          await markQuestionAnswer(answer.questionId, answer.isCorrect);
        }
      }

      console.log('Lesson completed with achievements tracking');
    } catch (error) {
      console.error('Error completing lesson with achievements:', error);
    }
  }, [markWordsLearned, markQuestionAnswer, saveLessonProgress]);

  return {
    completeLessonWithAchievements,
  };
};
