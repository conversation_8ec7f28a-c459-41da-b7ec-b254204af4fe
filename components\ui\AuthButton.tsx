import React from "react";
import { TouchableOpacity, Text, View, Image, ActivityIndicator, StyleSheet } from "react-native";

interface ButtonProps {
   title: string;
   image: any;
   loading?: boolean;
   onClick: () => void;
}

const AuthButton: React.FC<ButtonProps> = ({ title, loading = false, image, onClick }) => {
   return (
      <TouchableOpacity onPress={onClick} style={styles.buttonContainer}>
         <View style={styles.container}>
            {loading ? (
               <ActivityIndicator size="small" color="#fff" />
            ) : (
               <View style={styles.content}>
                  <Image source={image} style={styles.image} />
                  <Text style={styles.title}>{title}</Text>
               </View>
            )}
         </View>
      </TouchableOpacity>
   );
};

const styles = StyleSheet.create({
   buttonContainer: {
      borderRadius: 26,
      marginVertical: 12,
      overflow: "hidden",
      borderWidth: 1,
      borderColor: "#04212F",
   },
   container: {
      width: 319,
      height: 56,
      justifyContent: "center",
      alignItems: "center",
   },
   content: {
      flexDirection: "row",
      alignItems: "center",
   },
   image: {
      marginRight: 20,
      width: 24,
      height: 24,
   },
   title: {
      fontWeight: "700",
      color: "#04212F",
      fontSize: 20,
   },
});

export default AuthButton;
