import Svg, { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Defs, Rect } from "react-native-svg";

const BrawlSvg = () => (
   <Svg width="22" height="23" viewBox="0 0 22 23" fill="none">
      <Path
         fillRule="evenodd"
         clipRule="evenodd"
         d="M11 2.25C5.89137 2.25 1.75 6.39137 1.75 11.5C1.75 16.6086 5.89137 20.75 11 20.75C16.1086 20.75 20.25 16.6086 20.25 11.5C20.25 6.39137 16.1086 2.25 11 2.25ZM0.25 11.5C0.25 5.56294 5.06294 0.75 11 0.75C16.9371 0.75 21.75 5.56294 21.75 11.5C21.75 17.4371 16.9371 22.25 11 22.25C5.06294 22.25 0.25 17.4371 0.25 11.5ZM11 8.40049C10.8811 8.57699 10.7345 8.83717 10.5164 9.22853L10.3853 9.46367C10.3755 9.48113 10.3655 9.49937 10.3551 9.51826C10.2465 9.71556 10.099 9.98359 9.85546 10.1685C9.6074 10.3568 9.30824 10.4227 9.09436 10.4697C9.07405 10.4742 9.05452 10.4785 9.03587 10.4827L8.78132 10.5403C8.31615 10.6456 8.02625 10.7129 7.83146 10.781C7.9519 10.962 8.15408 11.201 8.48204 11.5845L8.65557 11.7874C8.66833 11.8024 8.68164 11.8178 8.69537 11.8337C8.84312 12.0047 9.03967 12.2322 9.13052 12.5244C9.22042 12.8135 9.18999 13.1126 9.16672 13.3412C9.16455 13.3626 9.16244 13.3833 9.1605 13.4034L9.13426 13.6741C9.08739 14.1577 9.05852 14.4706 9.05934 14.6919C9.25155 14.6238 9.51481 14.5042 9.91659 14.3192L10.1549 14.2095C10.1725 14.2014 10.1908 14.1928 10.21 14.1839C10.4078 14.0913 10.6885 13.9599 11 13.9599C11.3115 13.9599 11.5922 14.0913 11.7901 14.1839C11.8092 14.1928 11.8275 14.2014 11.8451 14.2095L12.0834 14.3192C12.4852 14.5042 12.7484 14.6238 12.9407 14.6919C12.9415 14.4706 12.9126 14.1577 12.8657 13.6741L12.8395 13.4034C12.8376 13.3833 12.8355 13.3626 12.8333 13.3412C12.81 13.1126 12.7796 12.8135 12.8695 12.5244C12.9603 12.2322 13.1569 12.0047 13.3046 11.8337C13.3184 11.8178 13.3317 11.8024 13.3444 11.7874L13.518 11.5845C13.8459 11.201 14.0481 10.962 14.1685 10.781C13.9737 10.7129 13.6838 10.6456 13.2187 10.5403L12.9641 10.4827C12.9455 10.4785 12.9259 10.4742 12.9056 10.4697C12.6918 10.4227 12.3926 10.3568 12.1445 10.1685C11.901 9.98359 11.7535 9.71556 11.6449 9.51827C11.6345 9.49937 11.6245 9.48113 11.6147 9.46367L11.4836 9.22853C11.2655 8.83717 11.1189 8.57699 11 8.40049ZM13.1529 14.7502C13.1541 14.7502 13.1547 14.7503 13.1547 14.7503L13.1529 14.7502ZM8.84533 14.7503C8.84535 14.7503 8.84594 14.7502 8.84706 14.7502L8.84533 14.7503ZM9.88315 7.38484C10.0988 7.10321 10.451 6.75 11 6.75C11.549 6.75 11.9012 7.10321 12.1169 7.38484C12.3245 7.65604 12.5371 8.03751 12.7683 8.4524C12.7768 8.46761 12.7853 8.48287 12.7938 8.49817L12.9249 8.73332C12.9617 8.79934 12.9902 8.85037 13.0155 8.89412C13.0324 8.92352 13.0458 8.94573 13.0565 8.96286C13.0733 8.96751 13.0946 8.97302 13.1219 8.97967C13.1689 8.99111 13.2235 9.00351 13.2952 9.01971L13.5497 9.0773C13.5671 9.08123 13.5844 9.08514 13.6016 9.08905C14.0485 9.19009 14.4657 9.28439 14.7807 9.40852C15.1219 9.54302 15.5343 9.78543 15.6932 10.2964C15.8496 10.7992 15.6571 11.2332 15.4626 11.5421C15.2799 11.8323 14.9979 12.162 14.6916 12.5201L14.4844 12.7623C14.4357 12.8193 14.3981 12.8633 14.3662 12.9018C14.3401 12.9334 14.322 12.9562 14.309 12.9734C14.3107 13.0291 14.3179 13.1075 14.3325 13.2587L14.3637 13.5803C14.4102 14.0596 14.4526 14.497 14.4373 14.8415C14.4213 15.1994 14.3369 15.6785 13.9114 16.0015C13.4737 16.3337 12.9847 16.2689 12.6378 16.1695C12.3132 16.0765 11.9231 15.8969 11.5049 15.7042C11.4886 15.6967 11.4724 15.6892 11.4561 15.6817L11.2177 15.572C11.1507 15.5411 11.0994 15.5175 11.0548 15.4979C11.0329 15.4882 11.0149 15.4805 11 15.4744C10.9851 15.4805 10.9671 15.4882 10.9452 15.4979C10.9006 15.5175 10.8493 15.5411 10.7823 15.572L10.5439 15.6817C10.5276 15.6892 10.5114 15.6967 10.4951 15.7042C10.0769 15.8969 9.68685 16.0765 9.3622 16.1695C9.01533 16.2689 8.52629 16.3337 8.08858 16.0015C7.66311 15.6785 7.57866 15.1994 7.56272 14.8415C7.54737 14.497 7.58981 14.0596 7.63631 13.5804L7.66749 13.2587C7.68214 13.1075 7.68928 13.0291 7.69097 12.9734C7.67803 12.9562 7.65995 12.9334 7.63378 12.9018C7.60187 12.8633 7.56431 12.8193 7.51557 12.7623L7.34204 12.5594C7.33081 12.5463 7.3196 12.5332 7.30843 12.5201C7.00207 12.162 6.72006 11.8323 6.53738 11.5421C6.34293 11.2332 6.15045 10.7992 6.3068 10.2964C6.46569 9.78543 6.87805 9.54302 7.21935 9.40852C7.53433 9.28439 7.95146 9.19009 8.39837 9.08905C8.41563 9.08514 8.43295 9.08123 8.4503 9.0773L8.70484 9.01971C8.77645 9.00351 8.83113 8.99112 8.87814 8.97967C8.90542 8.97302 8.92669 8.96751 8.94352 8.96287C8.95424 8.94573 8.96756 8.92352 8.98455 8.89412C9.00982 8.85037 9.03829 8.79934 9.07509 8.73332L9.20617 8.49817C9.2147 8.48287 9.22321 8.46761 9.23169 8.45239C9.46289 8.03751 9.67547 7.65603 9.88315 7.38484Z"
         fill="#00AFCA"
      />
   </Svg>
);

export default BrawlSvg;
