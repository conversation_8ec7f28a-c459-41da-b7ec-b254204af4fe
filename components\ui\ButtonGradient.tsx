import React from "react";
import {
   TouchableOpacity,
   Text,
   ActivityIndicator,
   StyleSheet,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";

interface ButtonProps {
   title: string;
   loading?: boolean;
   handlePress: () => void;
}

const ButtonGradient: React.FC<ButtonProps> = ({
   title,
   loading = false,
   handlePress,
}) => {
   return (
      <TouchableOpacity onPress={handlePress} style={styles.buttonContainer}>
         <LinearGradient
            colors={["#03D8F9", "#700AFF"]}
            style={styles.container}
         >
            {loading ? (
               <ActivityIndicator size="small" color="#fff" />
            ) : (
               <Text style={styles.title}>{title}</Text>
            )}
         </LinearGradient>
      </TouchableOpacity>
   );
};

const styles = StyleSheet.create({
   buttonContainer: {
      borderRadius: 16,
      overflow: "hidden",
      marginHorizontal: "auto",
   },
   container: {
      width: 319,
      height: 56,
      justifyContent: "center",
      alignItems: "center",
   },
   title: {
      fontWeight: "700",
      color: "#fff",
      fontSize: 20,
   },
});

export default ButtonGradient;
