import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React from "react";
import LevelOrnamentSvg from "../../assets/svg/LevelOrnamentSvg";

export default function LessonCircle({
   lessonNumber,
   title,
   level,
   handlePress,
   isAvailable,
}: {
   lessonNumber: number;
   title: string;
   level: number;
   isAvailable: boolean;
   handlePress?: () => void;
}) {
   return (
      <TouchableOpacity onPress={handlePress} style={{ marginTop: 38, marginLeft: 31 }}>
         <View
            style={{
               width: 312,
               height: 312,
               borderRadius: 312,
               backgroundColor: isAvailable ? "#D2E8FF" : "#fff",
            }}
         >
            <View
               style={{
                  width: 280,
                  height: 280,
                  borderRadius: 280,
                  backgroundColor: isAvailable ? "#0165B2" : "#C7C7CC",
                  margin: "auto",
               }}
            >
               <Text
                  style={{
                     fontWeight: 700,
                     fontSize: 12,
                     color: "#fff",
                     marginHorizontal: "auto",
                     textAlign: "center",
                     marginTop: 27,
                  }}
               >
                  Урок {lessonNumber}
               </Text>
               <Text
                  style={{
                     fontWeight: 700,
                     fontSize: 20,
                     color: "#fff",
                     marginHorizontal: "auto",
                     textAlign: "center",
                     width: "80%",
                     marginTop: 10,
                     marginBottom: 15,
                  }}
               >
                  {title}
               </Text>

               <View
                  style={{
                     display: "flex",
                     flexDirection: "row",
                     marginHorizontal: 29,
                     marginTop: 21,
                  }}
               >
                  <View
                     style={{
                        width: 72,
                        height: 72,
                        borderRadius: 72,
                        backgroundColor: "#fff",
                        marginTop: 20,
                     }}
                  >
                     <View
                        style={{
                           margin: "auto",
                           transform: [{ rotate: "-30deg" }],
                        }}
                     >
                        <LevelOrnamentSvg color={level >= 1 ? "#F7AD3A" : "#C7C7CC"} />
                     </View>
                  </View>

                  <View
                     style={{
                        width: 72,
                        height: 72,
                        borderRadius: 72,
                        backgroundColor: "#fff",
                        marginHorizontal: 3,
                     }}
                  >
                     <View style={{ margin: "auto" }}>
                        <LevelOrnamentSvg color={level >= 2 ? "#F7AD3A" : "#C7C7CC"} />
                     </View>
                  </View>
                  <View
                     style={{
                        width: 72,
                        height: 72,
                        borderRadius: 72,
                        backgroundColor: "#fff",
                        marginTop: 20,
                     }}
                  >
                     <View
                        style={{
                           margin: "auto",
                           transform: [{ rotate: "30deg" }],
                        }}
                     >
                        <LevelOrnamentSvg color={level >= 3 ? "#F7AD3A" : "#C7C7CC"} />
                     </View>
                  </View>
               </View>
            </View>
         </View>
      </TouchableOpacity>
   );
}
