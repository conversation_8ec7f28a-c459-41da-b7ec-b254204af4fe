import { Text, View } from "react-native";
import React, { useEffect, useState } from "react";
import DailyQuesItem from "../../components/DailyQuesItem";
import { UserDailyQuest } from "../../types/module";
import { getUserDailyQuests, getDailyQuestStats } from "../../utils/service";
import { useAuth } from "../../context/AuthContext";

const QuestsContent = () => {
   const { authState } = useAuth();
   const [userQuests, setUserQuests] = useState<UserDailyQuest[]>([]);
   const [questStats, setQuestStats] = useState({
      completed_today: 0,
      total_today: 0,
      streak: 0,
      total_rewards: 0,
   });
   const [isLoading, setIsLoading] = useState<boolean>(true);

   useEffect(() => {
      const fetchData = async () => {
         try {
            if (!authState.user?.id) {
               setIsLoading(false);
               return;
            }

            const [questsResponse, statsResponse] = await Promise.all([
               getUserDailyQuests(authState.user.id),
               getDailyQuestStats(authState.user.id),
            ]);

            setUserQuests(questsResponse.user_quests);
            setQuestStats(statsResponse);
            setIsLoading(false);
         } catch (error) {
            setIsLoading(false);
            console.error("Error fetching daily quests data:", error);
            // Fallback to mock data if API fails
            setUserQuests([]);
            setQuestStats({
               completed_today: 0,
               total_today: 3,
               streak: 0,
               total_rewards: 0,
            });
         }
      };

      fetchData();
   }, [authState.user?.id]);

   if (isLoading) {
      return (
         <View style={{ backgroundColor: "#fff", height: "100%", justifyContent: "center", alignItems: "center" }}>
            <Text>Загрузка заданий...</Text>
         </View>
      );
   }

   return (
      <View style={{ backgroundColor: "#fff", height: "100%" }}>
         <Text
            style={{
               fontWeight: 600,
               fontSize: 20,
               paddingHorizontal: 15,
               paddingTop: 20,
               color: "#242B35",
            }}
         >
            Ежедневные задания
         </Text>

         <Text
            style={{
               fontWeight: 600,
               fontSize: 16,
               paddingHorizontal: 17,
               paddingVertical: 20,
               color: "#F7AD3A",
            }}
         >
            {questStats.completed_today} из {questStats.total_today}
         </Text>

         {questStats.streak > 0 && (
            <Text
               style={{
                  fontWeight: 500,
                  fontSize: 14,
                  paddingHorizontal: 17,
                  color: "#4CAF50",
                  marginBottom: 10,
               }}
            >
               🔥 Стрик: {questStats.streak} дней
            </Text>
         )}

         <View>
            {userQuests.length > 0 ? (
               userQuests.map((userQuest, index) => {
                  return (
                     <View key={index} style={{ marginBottom: 17 }}>
                        <DailyQuesItem
                           title={userQuest.quest.title}
                           taskCount={userQuest.quest.target}
                           taskPassed={userQuest.progress}
                           isPassed={userQuest.completed}
                        />
                     </View>
                  );
               })
            ) : (
               <View style={{ paddingHorizontal: 17, marginTop: 20 }}>
                  <Text style={{ color: "#8E8E93", textAlign: "center" }}>
                     Сегодня нет доступных заданий
                  </Text>
               </View>
            )}
         </View>
      </View>
   );
};

export default QuestsContent;
