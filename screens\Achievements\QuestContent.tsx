import { Text, View } from "react-native";
import DailyQuesItem from "../../components/DailyQuesItem";

const mockQuests = [
   {
      title: "Выполните 1 урок",
      taskCount: 1,
      taskPassed: 0,
   },
   {
      title: "Прослушайте 7 заданий",
      taskCount: 7,
      taskPassed: 0,
   },
   {
      title: "Добавьте 1 друга",
      taskCount: 1,
      taskPassed: 0,
   },
];

const QuestsContent = () => {
   return (
      <View style={{ backgroundColor: "#fff", height: "100%" }}>
         <Text
            style={{
               fontWeight: 600,
               fontSize: 20,
               paddingHorizontal: 15,
               paddingTop: 20,
               color: "#242B35",
            }}
         >
            Ежедневные задания
         </Text>

         <Text
            style={{
               fontWeight: 600,
               fontSize: 16,
               paddingHorizontal: 17,
               paddingVertical: 20,
               color: "#F7AD3A",
            }}
         >
            0 из 5
         </Text>

         <View>
            {mockQuests.map((item, index) => {
               return (
                  <View key={index} style={{ marginBottom: 17 }}>
                     <DailyQuesItem
                        title={item.title}
                        taskCount={item.taskCount}
                        taskPassed={item.taskPassed}
                     />
                  </View>
               );
            })}
         </View>
      </View>
   );
};

export default QuestsContent;
