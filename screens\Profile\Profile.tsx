import { Text, SafeAreaView, StatusBar, View, ScrollView } from "react-native";
import React from "react";
import { SafeAreaProvider } from "react-native-safe-area-context";
import ProfileHeader from "./ProfileHeader";
import { Arrow } from "../../assets/image";
import { AchievementCard } from "../Achievements/AchievementCard";
import HalfOrnamentSvg from "../../assets/svg/HalfOrnamentSvg";
import { useAuth } from "../../context/AuthContext";

export default function Profile() {
   const { authState } = useAuth();
   return (
      <SafeAreaProvider>
         <ProfileHeader />
         <StatusBar barStyle={"dark-content"} />
         <SafeAreaView
            style={{
               backgroundColor: "#fff",
               height: "100%",
            }}
         >
            <ScrollView>
               <View
                  style={{
                     width: "90%",
                     marginHorizontal: "auto",
                     marginTop: 14,
                  }}
               >
                  <Text style={{ color: "#242B35", fontWeight: 600, fontSize: 20 }}>Статистика</Text>
               </View>

               <View
                  style={{
                     width: "90%",
                     height: 120,
                     borderWidth: 2,
                     borderRadius: 8,
                     borderColor: "#E1E1E1",
                     marginHorizontal: "auto",
                     marginTop: 20,
                     display: "flex",
                     flexDirection: "row",
                  }}
               >
                  <View style={{ marginVertical: "auto" }}>
                     <HalfOrnamentSvg />
                  </View>

                  <View style={{ marginVertical: "auto", marginLeft: 35 }}>
                     <Text
                        style={{
                           fontWeight: 600,
                           fontSize: 24,
                           color: "#242B35",
                           marginBottom: 15,
                        }}
                     >
                        5 дней темпа{" "}
                     </Text>
                     <Text
                        style={{
                           fontWeight: 600,
                           fontSize: 24,
                           color: "#242B35",
                        }}
                     >
                        155 XP
                     </Text>
                  </View>
               </View>

               <View
                  style={{
                     width: "90%",
                     marginHorizontal: "auto",
                     marginTop: 14,
                     marginBottom: 20,
                  }}
               >
                  <Text style={{ color: "#242B35", fontWeight: 600, fontSize: 20 }}>Достижения</Text>
               </View>

               <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{ width: "90%", marginHorizontal: "auto" }}>
                  <AchievementCard
                     image={Arrow}
                     title={"7 дней темпа"}
                     maxCount={7}
                     currentCount={7}
                     bgColor={"#F7AD3A"}
                     textColor={"#242B35"}
                  />
               </ScrollView>
            </ScrollView>
         </SafeAreaView>
      </SafeAreaProvider>
   );
}
