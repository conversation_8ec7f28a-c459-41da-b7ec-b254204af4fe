import Svg, { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Defs, Rect } from "react-native-svg";

const HistorySvg = () => (
   <Svg width="64" height="57" viewBox="0 0 64 57" fill="none">
      <G clip-path="url(#clip0_294_1928)">
         <G clip-path="url(#clip1_294_1928)">
            <Path
               fill-rule="evenodd"
               clip-rule="evenodd"
               d="M51.6579 22.1636L51.2324 26.7466C50.7358 32.0965 50.4874 34.7714 49.2171 36.6958C48.2141 38.2153 46.7831 39.4032 45.1049 40.1093C44.8952 40.1976 44.6802 40.2747 44.4578 40.3415C42.4257 40.9523 39.7768 40.7063 34.9548 40.2587C29.605 39.762 26.93 39.5137 25.0056 38.2434C23.4861 37.2404 22.2982 35.8093 21.5921 34.1312C20.6977 32.0058 20.9461 29.3309 21.4427 23.981L21.8682 19.398C21.9396 18.6288 22.0059 17.9144 22.0708 17.2501C22.4574 13.2943 22.7959 11.0965 23.8835 9.44885C24.8865 7.92938 26.3176 6.74145 27.9957 6.03529C30.1211 5.14096 32.796 5.38929 38.1458 5.88595C43.4957 6.3826 46.1706 6.63094 48.095 7.90123C49.6145 8.90424 50.8024 10.3353 51.5085 12.0135C52.4029 14.1388 52.1545 16.8137 51.6579 22.1636ZM29.1266 23.8898C29.2148 22.9406 30.0557 22.2426 31.0048 22.3307L42.4624 23.3944C43.4116 23.4825 44.1096 24.3234 44.0215 25.2726C43.9334 26.2218 43.0925 26.9198 42.1433 26.8317L30.6857 25.768C29.7366 25.6799 29.0385 24.839 29.1266 23.8898ZM28.4874 30.7638C28.5755 29.8146 29.4164 29.1166 30.3656 29.2047L37.2401 29.8429C38.1893 29.931 38.8874 30.7719 38.7992 31.7211C38.7111 32.6703 37.8702 33.3683 36.921 33.2802L30.0465 32.642C29.0973 32.5539 28.3993 31.713 28.4874 30.7638Z"
               fill="#F46700"
            />
            <Path
               opacity="0.5"
               d="M44.4574 40.3417C44.2318 41.8706 43.6241 43.3228 42.685 44.5609C41.2915 46.3981 38.8508 47.5205 33.9694 49.7653C29.088 52.0101 26.6472 53.1325 24.3455 52.9946C22.5281 52.8857 20.7838 52.2403 19.3332 51.1401C17.496 49.7467 16.3736 47.3059 14.1288 42.4245L12.2057 38.2428C9.96093 33.3613 8.83853 30.9206 8.97644 28.6189C9.08534 26.8015 9.73071 25.0572 10.831 23.6065C12.2244 21.7693 14.6651 20.6469 19.5465 18.4021C20.47 17.9775 21.3062 17.5929 22.0706 17.2495C22.0706 17.2493 22.0705 17.2498 22.0706 17.2495C22.0057 17.9138 21.9394 18.6289 21.868 19.3981L21.4426 23.9812C20.9459 29.331 20.6976 32.0059 21.5919 34.1313C22.2981 35.8094 23.486 37.2405 25.0055 38.2435C26.9299 39.5138 29.6048 39.7621 34.9546 40.2588C39.7765 40.7064 42.4254 40.9523 44.4574 40.3417Z"
               fill="#F46700"
            />
         </G>
      </G>
      <Defs>
         <ClipPath id="clip0_294_1928">
            <Rect
               x="0.253662"
               y="5.9292"
               width="55.2329"
               height="55.2329"
               rx="11.5068"
               transform="rotate(-9.69608 0.253662 5.9292)"
               fill="white"
            />
         </ClipPath>
         <ClipPath id="clip1_294_1928">
            <Rect
               x="0.253662"
               y="5.9292"
               width="55.2329"
               height="55.2329"
               rx="11.5068"
               transform="rotate(-9.69608 0.253662 5.9292)"
               fill="white"
            />
         </ClipPath>
      </Defs>
   </Svg>
);

export default HistorySvg;
