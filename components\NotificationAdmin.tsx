import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
  Picker,
} from 'react-native';
import { 
  sendAdminNotification, 
  sendBulkNotification, 
  sendGroupNotification,
  AdminNotificationRequest,
  BulkNotificationRequest,
  GroupNotificationRequest 
} from '../utils/service';
import { useNotificationStats } from '../utils/useNotifications';

const NotificationAdmin: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [targetType, setTargetType] = useState<'user' | 'bulk' | 'group' | 'all'>('user');
  const [userIds, setUserIds] = useState('');
  const [groupType, setGroupType] = useState('all');
  const [groupValue, setGroupValue] = useState('');
  const [notificationType, setNotificationType] = useState<'achievement' | 'reminder' | 'new_content' | 'progress' | 'welcome' | 'streak_lost' | 'streak_milestone' | 'announcement'>('announcement');
  const [title, setTitle] = useState('');
  const [body, setBody] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [customData, setCustomData] = useState('');

  const { stats, isLoading: statsLoading, loadStats } = useNotificationStats();

  const handleSendNotification = async () => {
    if (!title.trim() || !body.trim()) {
      Alert.alert('Ошибка', 'Заполните заголовок и текст уведомления');
      return;
    }

    try {
      setIsLoading(true);

      let data: any = {};
      if (customData.trim()) {
        try {
          data = JSON.parse(customData);
        } catch (e) {
          Alert.alert('Ошибка', 'Неверный формат JSON в дополнительных данных');
          return;
        }
      }

      const baseNotification = {
        type: notificationType,
        title: title.trim(),
        body: body.trim(),
        image_url: imageUrl.trim() || undefined,
        data: Object.keys(data).length > 0 ? data : undefined,
      };

      let response;

      switch (targetType) {
        case 'user':
          if (!userIds.trim()) {
            Alert.alert('Ошибка', 'Введите ID пользователей');
            return;
          }
          const userIdArray = userIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
          if (userIdArray.length === 0) {
            Alert.alert('Ошибка', 'Введите корректные ID пользователей');
            return;
          }
          
          if (userIdArray.length === 1) {
            response = await sendAdminNotification({
              target_type: 'user',
              user_ids: userIdArray,
              ...baseNotification,
            } as AdminNotificationRequest);
          } else {
            response = await sendBulkNotification({
              user_ids: userIdArray,
              ...baseNotification,
            } as BulkNotificationRequest);
          }
          break;

        case 'group':
          response = await sendGroupNotification({
            group_type: groupType as any,
            group_value: groupValue.trim() || undefined,
            ...baseNotification,
          } as GroupNotificationRequest);
          break;

        case 'all':
          response = await sendAdminNotification({
            target_type: 'all',
            ...baseNotification,
          } as AdminNotificationRequest);
          break;

        default:
          Alert.alert('Ошибка', 'Выберите тип получателей');
          return;
      }

      if (response.status === 'success') {
        Alert.alert(
          'Успех!', 
          `Уведомление отправлено!\n\nОтправлено: ${response.stats?.total_sent}\nУспешно: ${response.stats?.total_success}\nОшибок: ${response.stats?.total_failed}\nСервис: ${response.stats?.service_used}`
        );
        
        // Очищаем форму
        setTitle('');
        setBody('');
        setImageUrl('');
        setCustomData('');
        setUserIds('');
        setGroupValue('');
        
        // Обновляем статистику
        loadStats();
      } else {
        Alert.alert('Ошибка', response.message);
      }
    } catch (error) {
      console.error('Error sending admin notification:', error);
      Alert.alert('Ошибка', 'Не удалось отправить уведомление');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Админ панель уведомлений</Text>

      {/* Статистика */}
      <View style={styles.statsContainer}>
        <Text style={styles.sectionTitle}>Статистика</Text>
        {statsLoading ? (
          <ActivityIndicator size="small" color="#007AFF" />
        ) : stats ? (
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{stats.total_sent}</Text>
              <Text style={styles.statLabel}>Отправлено</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{stats.total_success}</Text>
              <Text style={styles.statLabel}>Успешно</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{stats.total_failed}</Text>
              <Text style={styles.statLabel}>Ошибок</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{(stats.success_rate * 100).toFixed(1)}%</Text>
              <Text style={styles.statLabel}>Успешность</Text>
            </View>
          </View>
        ) : (
          <Text style={styles.noStats}>Статистика недоступна</Text>
        )}
      </View>

      {/* Форма отправки */}
      <View style={styles.formContainer}>
        <Text style={styles.sectionTitle}>Отправить уведомление</Text>

        {/* Тип получателей */}
        <Text style={styles.label}>Получатели:</Text>
        <Picker
          selectedValue={targetType}
          onValueChange={setTargetType}
          style={styles.picker}
        >
          <Picker.Item label="Конкретные пользователи" value="user" />
          <Picker.Item label="Группа пользователей" value="group" />
          <Picker.Item label="Все пользователи" value="all" />
        </Picker>

        {/* ID пользователей */}
        {targetType === 'user' && (
          <>
            <Text style={styles.label}>ID пользователей (через запятую):</Text>
            <TextInput
              style={styles.input}
              value={userIds}
              onChangeText={setUserIds}
              placeholder="1, 2, 3"
              keyboardType="numeric"
            />
          </>
        )}

        {/* Группа */}
        {targetType === 'group' && (
          <>
            <Text style={styles.label}>Тип группы:</Text>
            <Picker
              selectedValue={groupType}
              onValueChange={setGroupType}
              style={styles.picker}
            >
              <Picker.Item label="Все активные" value="active" />
              <Picker.Item label="Неактивные" value="inactive" />
              <Picker.Item label="По роли" value="role" />
              <Picker.Item label="По уровню" value="level" />
            </Picker>

            {(groupType === 'role' || groupType === 'level') && (
              <>
                <Text style={styles.label}>Значение:</Text>
                <TextInput
                  style={styles.input}
                  value={groupValue}
                  onChangeText={setGroupValue}
                  placeholder={groupType === 'role' ? 'admin, user' : 'beginner, intermediate'}
                />
              </>
            )}
          </>
        )}

        {/* Тип уведомления */}
        <Text style={styles.label}>Тип уведомления:</Text>
        <Picker
          selectedValue={notificationType}
          onValueChange={setNotificationType}
          style={styles.picker}
        >
          <Picker.Item label="🏆 Достижение" value="achievement" />
          <Picker.Item label="⏰ Напоминание" value="reminder" />
          <Picker.Item label="📚 Новый контент" value="new_content" />
          <Picker.Item label="📈 Прогресс" value="progress" />
          <Picker.Item label="👋 Приветствие" value="welcome" />
          <Picker.Item label="💔 Потеря серии" value="streak_lost" />
          <Picker.Item label="🔥 Веха серии" value="streak_milestone" />
          <Picker.Item label="📢 Объявление" value="announcement" />
        </Picker>

        {/* Заголовок */}
        <Text style={styles.label}>Заголовок:</Text>
        <TextInput
          style={styles.input}
          value={title}
          onChangeText={setTitle}
          placeholder="Заголовок уведомления"
          maxLength={100}
        />

        {/* Текст */}
        <Text style={styles.label}>Текст:</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={body}
          onChangeText={setBody}
          placeholder="Текст уведомления"
          multiline
          numberOfLines={3}
          maxLength={500}
        />

        {/* URL изображения */}
        <Text style={styles.label}>URL изображения (опционально):</Text>
        <TextInput
          style={styles.input}
          value={imageUrl}
          onChangeText={setImageUrl}
          placeholder="https://example.com/image.jpg"
          keyboardType="url"
        />

        {/* Дополнительные данные */}
        <Text style={styles.label}>Дополнительные данные (JSON):</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={customData}
          onChangeText={setCustomData}
          placeholder='{"key": "value"}'
          multiline
          numberOfLines={2}
        />

        {/* Кнопка отправки */}
        <TouchableOpacity
          style={[styles.sendButton, isLoading && styles.sendButtonDisabled]}
          onPress={handleSendNotification}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.sendButtonText}>Отправить уведомление</Text>
          )}
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 20,
  },
  statsContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  noStats: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
  },
  formContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginTop: 15,
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  picker: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
  },
  sendButton: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  sendButtonDisabled: {
    backgroundColor: '#ccc',
  },
  sendButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default NotificationAdmin;
