import React from "react";
import { TouchableOpacity, Text, StyleSheet, View } from "react-native";

interface ButtonProps {
   title: string;
   handlePress: () => void;
   isPicked: boolean;
   isValid?: boolean;
}

const PairWordButton: React.FC<ButtonProps> = ({
   title,
   handlePress,
   isPicked,
   isValid = true,
}) => {
   const bgColor = isPicked ? "#bfebf1" : "#fff";
   return (
      <TouchableOpacity
         disabled={!isValid}
         onPress={handlePress}
         style={styles.buttonContainer}
      >
         <View
            style={{
               ...styles.button,
               backgroundColor: isValid ? bgColor : "#E2E2E2",

               borderColor: isPicked ? "#00AFCA80" : "#E1E1E1",
            }}
         >
            <View style={styles.levelContainer}>
               <Text style={styles.buttonText}>{title}</Text>
            </View>
         </View>
      </TouchableOpacity>
   );
};

const styles = StyleSheet.create({
   buttonContainer: {
      width: "40%",
      marginHorizontal: "auto",
      alignItems: "center",
      marginBottom: 15,
   },
   button: {
      width: "100%",
      height: 54,
      borderWidth: 1.4,
      borderRadius: 15,
   },
   levelContainer: {
      width: "100%",
      height: "100%",
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
   },
   buttonText: {
      fontWeight: "500",
      fontSize: 16,
      lineHeight: 18,
      color: "#3D3D3D",
      margin: "auto",
   },
});

export default PairWordButton;
