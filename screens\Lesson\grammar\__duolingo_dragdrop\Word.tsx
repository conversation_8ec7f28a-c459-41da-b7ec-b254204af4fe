import React from "react";
import { View, Text, StyleSheet, Platform } from "react-native";

import { WORD_HEIGHT } from "./Layout";

const styles = StyleSheet.create({
   root: {
      padding: 4,
   },
   container: {
      padding: 8,

      borderRadius: 8,
      borderWidth: 1,
      borderColor: "#E8E6E8",
      backgroundColor: "white",
      height: WORD_HEIGHT - 16,
   },
   text: {
      // fontFamily: "Inter",
      fontSize: 12,
      textAlign: "center",
      paddingHorizontal: 3,
      // fontWeight: 500,
   },
   shadow: {
      ...StyleSheet.absoluteFillObject,
      borderRadius: 8,
      borderBottomWidth: 1,
      borderColor: "#E8E6E8",
      top: 4,
   },
});

interface WordProps {
   id: number;
   word: string;
}

const Word = ({ word }: WordProps) => {
   return (
      <View style={styles.root}>
         <View>
            <View style={styles.container}>
               <Text style={styles.text}>{word}</Text>
            </View>
            <View style={styles.shadow} />
         </View>
      </View>
   );
};

export default Word;
