import { Platform, SafeAreaView, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React from "react";
import SettingsSvg from "../../assets/svg/SettingsSvg";
import RocketSvg from "../../assets/svg/RocketSvg";
import { useNavigation } from "@react-navigation/native";
import { useAuth } from "../../context/AuthContext";

export default function ProfileHeader() {
   const { navigate } = useNavigation();
   const { authState } = useAuth();

   const getDateStr = (dateStr: string) => {
      const date = new Date(dateStr);
      const options = { month: "long", year: "numeric" };
      // @ts-ignore
      const formattedDate = `С ${new Intl.DateTimeFormat("ru-RU", options).format(date)}`;
      return formattedDate;
   };
   return (
      <SafeAreaView
         style={{
            width: "100%",
            backgroundColor: "#ACEBFB",
            paddingTop: Platform.OS === "android" ? 25 : 0,
         }}
      >
         <TouchableOpacity
            style={{
               width: "90%",
               marginHorizontal: "auto",
               display: "flex",
               flexDirection: "row",
               justifyContent: "flex-end",
            }}
            onPress={() => {
               //@ts-ignore
               navigate("ProfileEdit");
            }}
         >
            <SettingsSvg />
         </TouchableOpacity>

         <View
            style={{
               marginHorizontal: "auto",
               height: 165,
               display: "flex",
               flexDirection: "column",
               justifyContent: "space-between",
               marginBottom: 15,
            }}
         >
            <View
               style={{
                  width: 80,
                  height: 80,
                  borderRadius: 80,
                  backgroundColor: "#fff",
                  borderColor: "#00AFCA",
                  borderWidth: 2,
                  borderStyle: "dashed",
                  marginHorizontal: "auto",
               }}
            >
               <Text
                  style={{
                     margin: "auto",
                     fontWeight: 600,
                     textAlign: "center",
                     fontSize: 32,
                     color: "#8E8E93",
                  }}
               >
                  {authState.user?.name.charAt(0)}
               </Text>
            </View>

            <View>
               <Text
                  style={{
                     fontWeight: 700,
                     fontSize: 20,
                     color: "#242B35",
                     marginHorizontal: "auto",
                  }}
               >
                  {authState.user?.name}
               </Text>
               <Text
                  style={{
                     fontWeight: 600,
                     fontSize: 12,
                     color: "#242B35",
                     marginHorizontal: "auto",
                     marginVertical: 10,
                  }}
               >
                  {getDateStr(authState.user?.created_at ?? "")}
               </Text>
               <Text
                  style={{
                     fontWeight: 600,
                     fontSize: 12,
                     color: "#242B35",
                     marginHorizontal: "auto",
                  }}
               >
                  12 друзей
               </Text>
            </View>
         </View>
      </SafeAreaView>
   );
}
