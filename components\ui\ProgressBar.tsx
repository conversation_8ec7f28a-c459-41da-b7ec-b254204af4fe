import React, { useEffect, useRef } from "react";
import { View, Text, StyleSheet, Animated } from "react-native";

interface ProgressBarProps {
   progress: number; // Прогресс в процентах (от 0 до 100)
   height?: number; // Высота прогресс-бара
   backgroundColor?: string; // Цвет фона
   progressColor?: string; // Цвет заполненной части
}

const ProgressBar: React.FC<ProgressBarProps> = ({
   progress = 0,
   height = 10,
   backgroundColor = "#e0e0e0",
   progressColor = "#32d583",
}) => {
   const animatedValue = useRef(new Animated.Value(0)).current;

   useEffect(() => {
      Animated.timing(animatedValue, {
         toValue: progress,
         duration: 500, // Длительность анимации
         useNativeDriver: false, // `useNativeDriver` не поддерживает ширину и высоту
      }).start();
   }, [progress]);

   const animatedWidth = animatedValue.interpolate({
      inputRange: [0, 100],
      outputRange: ["0%", "100%"],
   });

   return (
      <View style={[styles.container, { height, backgroundColor }]}>
         <Animated.View
            style={[
               styles.progress,
               { width: animatedWidth, backgroundColor: progressColor },
            ]}
         />
         <Text style={styles.label}>{Math.round(progress)}%</Text>
      </View>
   );
};

const styles = StyleSheet.create({
   container: {
      width: "90%",
      marginHorizontal: "auto",
      borderRadius: 16,
      overflow: "hidden",
      justifyContent: "center",
   },
   progress: {
      height: "100%",
      borderRadius: 16,
   },
   label: {
      position: "absolute",
      alignSelf: "center",
      color: "#fff",
      fontWeight: "bold",
   },
});

export default ProgressBar;
