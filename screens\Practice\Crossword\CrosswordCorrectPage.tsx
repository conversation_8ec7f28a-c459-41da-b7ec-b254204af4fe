import { StyleSheet, Text, View } from "react-native";
import React from "react";
import QoshqarSvg from "../../../assets/svg/QoshqarSvg";
import ChoiceRequiredButton from "../../../components/ui/ChoiceRequiredButton";
import { useNavigation } from "@react-navigation/native";

export default function CrosswordCorrectPage() {
   const { navigate } = useNavigation();

   return (
      <View style={{ backgroundColor: "#F1C644", height: "100%" }}>
         <View
            style={{
               marginVertical: "auto",
               display: "flex",
               flexDirection: "column",
               justifyContent: "space-between",
               width: "90%",
               height: 520,
               backgroundColor: "#fff",
               marginHorizontal: "auto",
               borderRadius: 12,
               paddingVertical: 52,
            }}
         >
            <View style={{ marginHorizontal: "auto" }}>
               <QoshqarSvg />
            </View>

            <View>
               <Text style={{ color: "#242B35", fontWeight: 700, fontSize: 24, marginHorizontal: "auto" }}>
                  Вы прошли уровень!
               </Text>

               <Text
                  style={{
                     color: "#8F8F91",
                     fontWeight: 500,
                     fontSize: 14,
                     textAlign: "center",
                     width: "90%",
                     marginHorizontal: "auto",
                     marginTop: 16,
                  }}
               >
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce suscipit metus quis ex molestie, in consectetur
                  quam eleifend. Nullam tempus dolor vel nisi bibendum tincidunt.
               </Text>
            </View>

            <ChoiceRequiredButton
               title="Далее"
               handlePress={() => {
                  // @ts-ignore
                  navigate("Practice");
               }}
               isValid={true}
            />
         </View>
      </View>
   );
}
