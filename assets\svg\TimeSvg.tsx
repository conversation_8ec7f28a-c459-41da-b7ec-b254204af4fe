import Svg, { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Defs, Rect } from "react-native-svg";

const TimeSvg = () => (
   <Svg width="21" height="23" viewBox="0 0 21 23" fill="none">
      <Path
         fillRule="evenodd"
         clipRule="evenodd"
         d="M7.75 1.5C7.75 1.08579 8.08579 0.75 8.5 0.75H12.5C12.9142 0.75 13.25 1.08579 13.25 1.5C13.25 1.91421 12.9142 2.25 12.5 2.25H8.5C8.08579 2.25 7.75 1.91421 7.75 1.5ZM10.5 4.25C5.94365 4.25 2.25 7.94365 2.25 12.5C2.25 17.0564 5.94365 20.75 10.5 20.75C15.0563 20.75 18.75 17.0564 18.75 12.5C18.75 7.94365 15.0563 4.25 10.5 4.25ZM0.75 12.5C0.75 7.11522 5.11522 2.75 10.5 2.75C15.8848 2.75 20.25 7.11522 20.25 12.5C20.25 17.8848 15.8848 22.25 10.5 22.25C5.11522 22.25 0.75 17.8848 0.75 12.5ZM10.5 7.75C10.9142 7.75 11.25 8.08579 11.25 8.5V12.5C11.25 12.9142 10.9142 13.25 10.5 13.25C10.0858 13.25 9.75 12.9142 9.75 12.5V8.5C9.75 8.08579 10.0858 7.75 10.5 7.75Z"
         fill="#F7AD3A"
      />
   </Svg>
);

export default TimeSvg;
