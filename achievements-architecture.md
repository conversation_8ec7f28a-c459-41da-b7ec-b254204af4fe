# Архитектура Системы Достижений

## Обзор

Система достижений в приложении изучения казахского языка построена на основе событийно-ориентированной архитектуры (Event-Driven Architecture) и обеспечивает автоматическое отслеживание прогресса пользователей.

## Компоненты системы

### 1. База данных

#### Таблица `achievements`
```sql
CREATE TABLE achievements (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    type TEXT NOT NULL,
    target INT NOT NULL,
    icon TEXT DEFAULT '',
    reward INT DEFAULT 0,
    category TEXT DEFAULT 'general',
    difficulty TEXT DEFAULT 'easy',
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Таблица `user_achievements`
```sql
CREATE TABLE user_achievements (
    id SERIAL PRIMARY KEY,
    user_id INT NOT NULL,
    achievement_id INT NOT NULL,
    progress INT DEFAULT 0,
    achieved BOOLEAN DEFAULT FALSE,
    achieved_at TIMESTAMP NULL,
    reward_claimed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(user_id, achievement_id)
);
```

### 2. Модели данных (Go)

#### Achievement
```go
type Achievement struct {
    ID          int                    `json:"id"`
    Name        string                 `json:"name"`
    Description string                 `json:"description"`
    Type        string                 `json:"type"`
    Target      int                    `json:"target"`
    Icon        string                 `json:"icon"`
    Reward      int                    `json:"reward"`
    Category    string                 `json:"category"`
    Difficulty  string                 `json:"difficulty"`
    IsActive    bool                   `json:"is_active"`
    Metadata    map[string]interface{} `json:"metadata"`
    CreatedAt   time.Time              `json:"created_at"`
    UpdatedAt   time.Time              `json:"updated_at"`
}
```

#### UserAchievement
```go
type UserAchievement struct {
    ID            int        `json:"id"`
    AchievementID int        `json:"achievement_id"`
    UserID        int        `json:"user_id"`
    Progress      int        `json:"progress"`
    Achieved      bool       `json:"achieved"`
    AchievedAt    *time.Time `json:"achieved_at,omitempty"`
    RewardClaimed bool       `json:"reward_claimed"`
    CreatedAt     time.Time  `json:"created_at"`
    UpdatedAt     time.Time  `json:"updated_at"`
    Achievement   *Achievement `json:"achievement,omitempty"`
}
```

### 3. Система событий

#### AchievementEvent
```go
type AchievementEvent struct {
    UserID    int                    `json:"user_id"`
    EventType string                 `json:"event_type"`
    Data      map[string]interface{} `json:"data"`
    Timestamp time.Time              `json:"timestamp"`
}
```

#### Типы событий
- `lesson_completed` - Урок завершен
- `word_learned` - Слово изучено
- `question_answered` - Ответ на вопрос
- `module_completed` - Модуль завершен
- `streak_updated` - Обновление стрика
- `progress_saved` - Прогресс сохранен

### 4. AchievementService

Центральный компонент для обработки событий и обновления достижений:

```go
type AchievementService struct {
    achievementModel     *AchievementModel
    redis                *redis.Client
    events               chan AchievementEvent
    stopCh               chan struct{}
    notificationCallback AchievementNotificationCallback
}
```

#### Основные методы:
- `Start()` - Запуск обработки событий
- `SendEvent(event)` - Отправка события
- `SetNotificationCallback()` - Установка callback для уведомлений

## Поток данных

### 1. Действие пользователя
```
Пользователь → Завершает урок → saveProgressHandler
```

### 2. Генерация события
```go
progressEvent := data.CreateAchievementEvent(userID, data.EventProgressSaved, map[string]interface{}{
    "module_id": moduleID,
    "questions_answered": questionsCount,
})
app.models.AchievementService.SendEvent(progressEvent)
```

### 3. Обработка события
```
AchievementService → handleEvent() → updateAchievementsWithNotification()
```

### 4. Обновление достижений
```
GetAchievementsByType() → UpdateAchievementProgressWithFullResult() → SendNotification()
```

### 5. Уведомление пользователя
```
NotificationCallback → sendEnhancedAchievementNotification() → Firebase Push
```

## Типы достижений и их обработка

### 1. Достижения за уроки (`lessons`)
- **Триггер**: Завершение урока через `saveProgressHandler`
- **Событие**: `lesson_completed`
- **Обновление**: +1 к прогрессу

### 2. Достижения за слова (`words`)
- **Триггер**: Отметка слов как изученных через `markWordAsLearnedHandler`
- **Событие**: `word_learned`
- **Обновление**: +количество слов к прогрессу

### 3. Достижения за вопросы (`questions`)
- **Триггер**: Ответ на вопрос через `markQuestionAnsweredHandler`
- **Событие**: `question_answered`
- **Обновление**: +1 к прогрессу

### 4. Достижения за модули (`modules`)
- **Триггер**: Завершение модуля через `saveProgressHandler`
- **Событие**: `module_completed`
- **Обновление**: +1 к прогрессу

### 5. Достижения за стрики (`streak`)
- **Триггер**: Обновление стрика пользователя
- **Событие**: `streak_updated`
- **Обновление**: Устанавливается текущий стрик

### 6. Достижения за точность (`consecutive`)
- **Триггер**: Правильный ответ на вопрос
- **Событие**: `question_answered` с `is_correct: true`
- **Обновление**: +1 к прогрессу или сброс при неправильном ответе

## Кэширование

### Redis кэш
- **Ключ**: `user_achievements:{user_id}`
- **TTL**: 5 минут
- **Данные**: Список достижений пользователя
- **Инвалидация**: При обновлении любого достижения пользователя

### Методы кэширования:
```go
func (m *AchievementModel) GetUserAchievementsWithCache(userID int64) ([]UserAchievement, error)
func (m *AchievementModel) InvalidateUserAchievementsCache(userID int64)
```

## Система уведомлений

### Персонализированные уведомления
Уведомления адаптируются в зависимости от категории достижения:

```go
switch achievement.Category {
case data.AchievementCategoryProgress:
    title = "🎯 Прогресс в обучении!"
    icon = "📈"
case data.AchievementCategoryVocabulary:
    title = "📚 Словарный запас!"
    icon = "📝"
// ... другие категории
}
```

### Структура уведомления
```go
type NotificationRequest struct {
    UserID int64
    Title  string
    Body   string
    Data   map[string]string
}
```

## Масштабируемость

### Горизонтальное масштабирование
- События обрабатываются асинхронно через каналы Go
- Буферизованный канал событий (100 событий)
- Graceful shutdown через `stopCh`

### Производительность
- Индексы на часто используемые поля
- Кэширование через Redis
- Batch обновления для множественных достижений

### Мониторинг
- Логирование всех событий и ошибок
- Метрики производительности
- Panic recovery в обработчиках событий

## Безопасность

### Валидация данных
```go
func (m *AchievementModel) ValidateAchievement(achievement *Achievement) error {
    // Проверка обязательных полей
    // Валидация типов и категорий
    // Проверка ограничений
}
```

### Права доступа
- Админские эндпоинты защищены middleware `adminMiddleware`
- Пользовательские эндпоинты требуют аутентификации
- Валидация прав на уровне API

### Ограничения
- Уникальность пары (user_id, achievement_id)
- Проверка положительных значений для target и reward
- Валидация enum значений для type, category, difficulty

## Развертывание

### Миграции базы данных
1. `000013_extend_achievements_table.up.sql` - Расширение таблиц
2. `000014_insert_default_achievements.up.sql` - Предустановленные достижения

### Конфигурация
- Запуск AchievementService в main.go
- Установка notification callback
- Настройка Redis для кэширования

### Мониторинг
- Логи запуска сервиса
- Метрики обработки событий
- Отслеживание ошибок уведомлений

## Будущие улучшения

### Планируемые функции
1. **Достижения с условиями** - сложные условия получения
2. **Временные достижения** - ограниченные по времени
3. **Групповые достижения** - достижения для команд
4. **Достижения с прогрессом** - многоуровневые достижения
5. **Аналитика достижений** - статистика и отчеты

### Оптимизации
1. **Batch processing** - обработка событий пакетами
2. **Event sourcing** - полная история событий
3. **CQRS** - разделение команд и запросов
4. **Микросервисная архитектура** - выделение в отдельный сервис
