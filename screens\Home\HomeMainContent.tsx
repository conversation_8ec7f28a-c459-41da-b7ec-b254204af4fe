import { <PERSON><PERSON>, ScrollView, View } from "react-native";
import React, { useEffect, useState } from "react";
import LessonCircle from "./LessonCircle";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { GetRequest } from "../../utils/service";
import { Module } from "../../types/module";
import { useAuth } from "../../context/AuthContext";

export default function HomeMainContent() {
   const { navigate } = useNavigation();
   const { authState } = useAuth();
   const [modules, setModules] = useState<Module[]>([]);
   const [passedModules, setPassedModules] = useState<number[]>([]);
   const [loading, setLoading] = useState(true);

   const fetchData = async () => {
      try {
         const [modulesResponse, userPassedResponse] = await Promise.all([
            GetRequest("/v1/module/all"),
            GetRequest(`/v1/module-user-progress/${authState.user?.id}`),
         ]);
         setModules(modulesResponse.modules ?? []);
         setPassedModules(userPassedResponse.passed_modules ?? []);
         setLoading(false);
      } catch (error) {
         console.log(error);
         setLoading(false);
      }
   };

   useEffect(() => {
      fetchData();
   }, []);

   useFocusEffect(() => {
      fetchData();
   });

   if (loading) {
      return;
   }
   return (
      <View style={{ flex: 1 }}>
         <ScrollView showsHorizontalScrollIndicator={false} horizontal style={{ marginBottom: 15 }}>
            {modules.map((module) => {
               const pre_requisite_ids = module.pre_requisite_ids ?? [];
               const isAvailable = pre_requisite_ids?.every((id) => passedModules.includes(id));

               return (
                  <LessonCircle
                     handlePress={() => {
                        if (isAvailable) {
                           // @ts-ignore
                           navigate("Lesson", { id: module.id });
                        } else {
                           Alert.alert("Недоступно", "Вам следует пройти предыдущие модули");
                        }
                     }}
                     lessonNumber={module.id}
                     title={module.name}
                     level={module.level}
                     isAvailable={isAvailable ?? false}
                     key={module.id}
                  />
               );
            })}
         </ScrollView>
      </View>
   );
}
