import React from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { Audio } from "expo-av";
import AudioSvg from "../../../assets/svg/AudioSvg";
import {
   EsikAudio,
   AiuAudio,
   EmhanaAudio,
   TauAudio,
} from "../../../assets/audio";

export default function HistoryContent() {
   return (
      <View style={{ width: "100%", marginTop: 12 }}>
         <ListeningButton word="Аю" audioFile={AiuAudio} />
         <ListeningButton word="Есік" audioFile={EsikAudio} />
         <ListeningButton word="Емхана" audioFile={EmhanaAudio} />
         <ListeningButton word="Тау" audioFile={TauAudio} />
      </View>
   );
}

const ListeningButton = ({
   word,
   audioFile,
}: {
   word: string;
   audioFile: any;
}) => {
   const playSound = async () => {
      const { sound } = await Audio.Sound.createAsync(audioFile);
      await sound.playAsync();
      // Не забываем очистить ресурс после использования
      sound.setOnPlaybackStatusUpdate((status) => {
         // @ts-ignore
         if (status.didJustFinish) {
            sound.unloadAsync(); // Очищаем звук после окончания воспроизведения
         }
      });
   };

   return (
      <View
         style={{
            width: "90%",
            marginHorizontal: "auto",
            height: 55,
            borderWidth: 1,
            borderColor: "#E1E1E1",
            borderRadius: 10,
            marginBottom: 15,
            backgroundColor: "#fff",
            shadowColor: "#003A42",
            shadowOffset: {
               width: 0,
               height: 3,
            },
            shadowOpacity: 0.25,
            shadowRadius: 5,
            elevation: 5,
         }}
      >
         <View
            style={{
               paddingHorizontal: 15,
               marginVertical: "auto",
               display: "flex",
               flexDirection: "row",
               justifyContent: "space-between",
               alignItems: "center",
            }}
         >
            <Text
               style={{
                  fontWeight: 500,
                  fontSize: 16,
               }}
            >
               {word}
            </Text>

            <TouchableOpacity onPress={playSound}>
               <AudioSvg />
            </TouchableOpacity>
         </View>
      </View>
   );
};
