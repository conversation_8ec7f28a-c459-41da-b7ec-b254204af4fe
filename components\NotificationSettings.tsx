import React, { useState } from 'react';
import {
  View,
  Text,
  Switch,
  StyleSheet,
  ScrollView,
  Alert,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { useNotificationSettings, useNotifications } from '../utils/useNotifications';

interface NotificationSettingsProps {
  onClose?: () => void;
}

const NotificationSettings: React.FC<NotificationSettingsProps> = ({ onClose }) => {
  const { settings, isLoading, updateSettings } = useNotificationSettings();
  const { permissionStatus, requestPermissions, sendTest } = useNotifications();
  const [isSaving, setIsSaving] = useState(false);

  // Обработка изменения настройки
  const handleSettingChange = async (key: string, value: boolean) => {
    try {
      setIsSaving(true);
      const success = await updateSettings({ [key]: value });
      
      if (!success) {
        Alert.alert('Ошибка', 'Не удалось обновить настройки');
      }
    } catch (error) {
      console.error('Error updating setting:', error);
      Alert.alert('Ошибка', 'Произошла ошибка при обновлении настроек');
    } finally {
      setIsSaving(false);
    }
  };

  // Запрос разрешений
  const handleRequestPermissions = async () => {
    const granted = await requestPermissions();
    if (granted) {
      Alert.alert('Успех', 'Разрешения на уведомления предоставлены');
    }
  };

  // Отправка тестового уведомления
  const handleSendTest = () => {
    Alert.alert(
      'Тестовое уведомление',
      'Отправить тестовое уведомление?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Отправить',
          onPress: () => sendTest('Тест', 'Это тестовое уведомление от Kazakh Lingo'),
        },
      ]
    );
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Загрузка настроек...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Настройки уведомлений</Text>
        {onClose && (
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Статус разрешений */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Разрешения</Text>
        <View style={styles.permissionRow}>
          <Text style={styles.permissionText}>
            Статус: {permissionStatus === 'granted' ? 'Разрешены' : 'Не разрешены'}
          </Text>
          {permissionStatus !== 'granted' && (
            <TouchableOpacity
              style={styles.permissionButton}
              onPress={handleRequestPermissions}
            >
              <Text style={styles.permissionButtonText}>Разрешить</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Основные настройки */}
      {settings && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Основные настройки</Text>
          
          <View style={styles.settingRow}>
            <Text style={styles.settingLabel}>Push уведомления</Text>
            <Switch
              value={settings.push_enabled}
              onValueChange={(value) => handleSettingChange('push_enabled', value)}
              disabled={isSaving || permissionStatus !== 'granted'}
            />
          </View>

          <View style={styles.settingRow}>
            <Text style={styles.settingLabel}>Уведомления о достижениях</Text>
            <Switch
              value={settings.achievements_enabled}
              onValueChange={(value) => handleSettingChange('achievements_enabled', value)}
              disabled={isSaving || !settings.push_enabled}
            />
          </View>

          <View style={styles.settingRow}>
            <Text style={styles.settingLabel}>Напоминания о занятиях</Text>
            <Switch
              value={settings.reminders_enabled}
              onValueChange={(value) => handleSettingChange('reminders_enabled', value)}
              disabled={isSaving || !settings.push_enabled}
            />
          </View>

          <View style={styles.settingRow}>
            <Text style={styles.settingLabel}>Уведомления о новом контенте</Text>
            <Switch
              value={settings.new_content_enabled}
              onValueChange={(value) => handleSettingChange('new_content_enabled', value)}
              disabled={isSaving || !settings.push_enabled}
            />
          </View>
        </View>
      )}

      {/* Время напоминаний */}
      {settings && settings.reminders_enabled && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Время напоминаний</Text>
          <Text style={styles.reminderTime}>
            Ежедневное напоминание: {settings.daily_reminder_time}
          </Text>
          <Text style={styles.reminderNote}>
            Для изменения времени обратитесь к настройкам профиля
          </Text>
        </View>
      )}

      {/* Тестирование */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Тестирование</Text>
        <TouchableOpacity
          style={[
            styles.testButton,
            (permissionStatus !== 'granted' || !settings?.push_enabled) && styles.testButtonDisabled
          ]}
          onPress={handleSendTest}
          disabled={permissionStatus !== 'granted' || !settings?.push_enabled}
        >
          <Text style={[
            styles.testButtonText,
            (permissionStatus !== 'granted' || !settings?.push_enabled) && styles.testButtonTextDisabled
          ]}>
            Отправить тестовое уведомление
          </Text>
        </TouchableOpacity>
      </View>

      {/* Информация */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Информация</Text>
        <Text style={styles.infoText}>
          • Уведомления помогают не пропустить важные события{'\n'}
          • Вы можете отключить любой тип уведомлений{'\n'}
          • Настройки синхронизируются между устройствами{'\n'}
          • Для работы уведомлений требуется подключение к интернету
        </Text>
      </View>

      {isSaving && (
        <View style={styles.savingOverlay}>
          <ActivityIndicator size="small" color="#007AFF" />
          <Text style={styles.savingText}>Сохранение...</Text>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 18,
    color: '#666',
  },
  section: {
    backgroundColor: '#fff',
    marginTop: 10,
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  permissionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  permissionText: {
    fontSize: 16,
    color: '#666',
  },
  permissionButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingLabel: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  reminderTime: {
    fontSize: 16,
    color: '#333',
    marginBottom: 5,
  },
  reminderNote: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  testButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  testButtonDisabled: {
    backgroundColor: '#ccc',
  },
  testButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  testButtonTextDisabled: {
    color: '#999',
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  savingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  savingText: {
    marginLeft: 10,
    fontSize: 16,
    color: '#007AFF',
  },
});

export default NotificationSettings;
