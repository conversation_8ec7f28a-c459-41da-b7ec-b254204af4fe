import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useNotifications } from '../utils/useNotifications';
import NotificationSettings from './NotificationSettings';
import NotificationHistory from './NotificationHistory';

const NotificationDemo: React.FC = () => {
  const [showSettings, setShowSettings] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const {
    isInitialized,
    token,
    tokenType,
    permissionStatus,
    requestPermissions,
    sendTest,
    sendPersonalNotification,
    clearNotifications
  } = useNotifications();

  const handleRequestPermissions = async () => {
    const granted = await requestPermissions();
    if (granted) {
      Alert.alert('Успех', 'Разрешения на уведомления предоставлены!');
    }
  };

  const handleSendTest = () => {
    Alert.alert(
      'Тестовое уведомление',
      'Отправить тестовое уведомление?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Отправить',
          onPress: () => sendTest('Тест от Kazakh Lingo', 'Это тестовое уведомление для проверки работы системы'),
        },
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'granted':
        return '#4CAF50';
      case 'denied':
        return '#F44336';
      default:
        return '#FF9800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'granted':
        return 'Разрешены';
      case 'denied':
        return 'Запрещены';
      case 'undetermined':
        return 'Не определены';
      default:
        return status;
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Push Уведомления</Text>
      
      {/* Статус инициализации */}
      <View style={styles.statusCard}>
        <Text style={styles.statusTitle}>Статус системы</Text>
        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>Инициализация:</Text>
          <Text style={[styles.statusValue, { color: isInitialized ? '#4CAF50' : '#F44336' }]}>
            {isInitialized ? 'Завершена' : 'В процессе'}
          </Text>
        </View>
        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>Разрешения:</Text>
          <Text style={[styles.statusValue, { color: getStatusColor(permissionStatus) }]}>
            {getStatusText(permissionStatus)}
          </Text>
        </View>
        <View style={styles.statusRow}>
          <Text style={styles.statusLabel}>Токен:</Text>
          <Text style={[styles.statusValue, { color: token ? '#4CAF50' : '#F44336' }]}>
            {token ? `Получен (${tokenType?.toUpperCase()})` : 'Отсутствует'}
          </Text>
        </View>
      </View>

      {/* Токен (для отладки) */}
      {token && (
        <View style={styles.tokenCard}>
          <Text style={styles.tokenTitle}>Push Token (для отладки)</Text>
          <Text style={styles.tokenText} numberOfLines={3}>
            {token}
          </Text>
        </View>
      )}

      {/* Кнопки управления */}
      <View style={styles.buttonsContainer}>
        {permissionStatus !== 'granted' && (
          <TouchableOpacity
            style={[styles.button, styles.primaryButton]}
            onPress={handleRequestPermissions}
          >
            <Text style={styles.buttonText}>Запросить разрешения</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={() => setShowSettings(true)}
        >
          <Text style={[styles.buttonText, styles.secondaryButtonText]}>
            Настройки уведомлений
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={() => setShowHistory(true)}
        >
          <Text style={[styles.buttonText, styles.secondaryButtonText]}>
            История уведомлений
          </Text>
        </TouchableOpacity>

        {permissionStatus === 'granted' && (
          <>
            <TouchableOpacity
              style={[styles.button, styles.testButton]}
              onPress={handleSendTest}
            >
              <Text style={styles.buttonText}>Отправить тест</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.achievementButton]}
              onPress={() => sendTest('🏆 Достижение!', 'Вы получили новое достижение!', 'achievement')}
            >
              <Text style={styles.buttonText}>Тест достижения</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.reminderButton]}
              onPress={() => sendTest('⏰ Напоминание', 'Время для изучения казахского языка!', 'reminder')}
            >
              <Text style={styles.buttonText}>Тест напоминания</Text>
            </TouchableOpacity>
          </>
        )}

        <TouchableOpacity
          style={[styles.button, styles.clearButton]}
          onPress={clearNotifications}
        >
          <Text style={styles.buttonText}>Очистить уведомления</Text>
        </TouchableOpacity>
      </View>

      {/* Модальные окна */}
      <Modal
        visible={showSettings}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <NotificationSettings onClose={() => setShowSettings(false)} />
        </SafeAreaView>
      </Modal>

      <Modal
        visible={showHistory}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <NotificationHistory onClose={() => setShowHistory(false)} />
        </SafeAreaView>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 30,
  },
  statusCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  statusLabel: {
    fontSize: 16,
    color: '#666',
  },
  statusValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  tokenCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tokenTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
  },
  tokenText: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
    backgroundColor: '#f8f8f8',
    padding: 10,
    borderRadius: 6,
  },
  buttonsContainer: {
    gap: 15,
  },
  button: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 10,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#007AFF',
  },
  secondaryButton: {
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  testButton: {
    backgroundColor: '#4CAF50',
  },
  achievementButton: {
    backgroundColor: '#FF9800',
  },
  reminderButton: {
    backgroundColor: '#2196F3',
  },
  clearButton: {
    backgroundColor: '#FF5722',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  secondaryButtonText: {
    color: '#007AFF',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
});

export default NotificationDemo;
