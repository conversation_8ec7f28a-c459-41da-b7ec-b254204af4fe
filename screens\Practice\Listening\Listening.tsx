import { StyleSheet, Text, View, StatusBar, SafeAreaView, Platform } from "react-native";
import React, { useState } from "react";
import { SafeAreaProvider } from "react-native-safe-area-context";
import ProgressBar from "../../../components/ui/ProgressBar";

import { EmhanaAudio, JastyqAudio, OrmanAudio } from "../../../assets/audio";
import { ListeningQuestion } from "./ListeningQuestion";
import { useNavigation } from "@react-navigation/native";

const __mockQuestions = [
   {
      id: 1,
      type: "kz-audio-to-kz-word",
      variants: [
         {
            id: 5165,
            plaintext: "Емхана",
            url: EmhanaAudio,
            voice_id: 1,
         },
         {
            id: 6158,
            plaintext: "Дәріхана",
            url: "https://storage.google.com/word/5165",
            voice_id: 1,
         },
         {
            id: 5995,
            plaintext: "Асхана",
            url: "https://storage.google.com/word/5165",
            voice_id: 1,
         },
      ],
      correct_answer: {
         id: 5165,
         plaintext: "Емхана",
         url: <PERSON>hanaAudio,
         voice_id: 1,
      },
   },
   {
      id: 2,
      type: "kz-audio-to-kz-word",
      variants: [
         {
            id: 1625,
            plaintext: "Мектеп",
            url: "https://storage.google.com/word/5165",
            voice_id: 1,
         },
         {
            id: 2626,
            plaintext: "Жастық",
            url: "https://storage.google.com/word/5165",
            voice_id: 1,
         },
         {
            id: 5691,
            plaintext: "Бесік",
            url: "https://storage.google.com/word/5165",
            voice_id: 1,
         },
      ],
      correct_answer: {
         id: 2626,
         plaintext: "Жастық",
         url: JastyqAudio,
         voice_id: 1,
      },
   },
   {
      id: 3,
      type: "kz-audio-to-ru-word",
      variants: [
         {
            id: 1295,
            plaintext: "Орман",
            url: OrmanAudio,
            voice_id: 1,
         },
         {
            id: 1698,
            plaintext: "Аула",
            url: JastyqAudio,
            voice_id: 1,
         },
         {
            id: 91951,
            plaintext: "Аспан",
            url: "https://storage.google.com/word/5165",
            voice_id: 1,
         },
      ],
      correct_answer: {
         id: 1295,
         plaintext: "Орман",
         url: OrmanAudio,
         voice_id: 1,
      },
   },
];

export default function Listening() {
   const { navigate } = useNavigation();

   const [mockQuestions, setMockQuestions] = useState(__mockQuestions);
   const totalLen = __mockQuestions.length;

   const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
   const [results, setResults] = useState<any[]>([]);

   const [progress, setProgress] = useState(0);

   const handleNextQuestion = () => {
      if (currentQuestionIndex + 1 == mockQuestions.length) {
         // @ts-ignore
         navigate("Practice");
         return;
      }
      setCurrentQuestionIndex((prev) => prev + 1);
   };

   const handleSaveResult = (isCorrect: boolean) => {
      if (isCorrect) {
         setProgress(progress + 100 / totalLen);
      }

      const result = {
         questionId: mockQuestions[currentQuestionIndex].id,
         isCorrect,
      };

      setResults([...results, result]);

      if (!isCorrect) {
         const incorrectQuestion = mockQuestions[currentQuestionIndex];
         setMockQuestions([...mockQuestions, incorrectQuestion]);
      }
   };

   return (
      <SafeAreaProvider style={{ backgroundColor: "#fff" }}>
         <StatusBar barStyle="dark-content" />
         <SafeAreaView style={{ marginTop: Platform.OS === "android" ? 50 : 0 }}>
            <ProgressBar progress={progress} height={18} />
            <ListeningQuestion
               question={mockQuestions[currentQuestionIndex]}
               onNextQuestion={handleNextQuestion}
               onSaveResult={handleSaveResult}
            />
         </SafeAreaView>
      </SafeAreaProvider>
   );
}
