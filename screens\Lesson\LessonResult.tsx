import { Platform, SafeAreaView, StatusBar, Text, View, StyleSheet } from "react-native";
import React, { useState } from "react";
import { SafeAreaProvider } from "react-native-safe-area-context";
import SunSvg from "../../assets/svg/SunSvg";
import ChoiceRequiredButton from "../../components/ui/ChoiceRequiredButton";
import TimeSvg from "../../assets/svg/TimeSvg";
import BrawlSvg from "../../assets/svg/BrawlSvg";
import { useNavigation } from "@react-navigation/native";

export default function LessonResult() {
   const { navigate } = useNavigation();
   return (
      <SafeAreaProvider style={styles.safeAreaProvider}>
         <StatusBar barStyle={"dark-content"} />
         <SafeAreaView style={styles.safeAreaView}>
            <View>
               <View style={styles.xpContainer}>
                  <Text style={styles.xpText}>+15 XP</Text>
                  <SunSvg />
               </View>

               <Text style={styles.lessonTitle}>Вы изучили новые слова</Text>
               <Text style={styles.lessonSubtitle}>
                  Они теперь в <Text style={styles.dictionaryLink}>словаре</Text>
               </Text>

               <View style={styles.statContainer}>
                  <View style={styles.statBox}>
                     <Text style={styles.statBoxTitle}>Быстро</Text>
                     <View style={styles.statContent}>
                        <TimeSvg />
                        <Text style={styles.statBoxValue}>04:12</Text>
                     </View>
                  </View>
                  <View style={styles.statBoxAlt}>
                     <Text style={styles.statBoxTitleAlt}>Отлично</Text>
                     <View style={styles.statContent}>
                        <BrawlSvg />
                        <Text style={styles.statBoxValueAlt}>100%</Text>
                     </View>
                  </View>
               </View>
            </View>

            <ChoiceRequiredButton
               isValid={true}
               handlePress={() => {
                  // @ts-ignore
                  navigate("Home");
               }}
               title="Далее"
            />
         </SafeAreaView>
      </SafeAreaProvider>
   );
}

const styles = StyleSheet.create({
   safeAreaProvider: {
      paddingTop: 150,
      paddingBottom: 50,
      backgroundColor: "#fff",
   },
   safeAreaView: {
      marginTop: Platform.OS === "android" ? 25 : 0,
      height: "100%",
      display: "flex",
      flexDirection: "column",
      justifyContent: "space-between",
   },
   xpContainer: {
      marginHorizontal: "auto",
      justifyContent: "center",
      alignItems: "center",
   },
   xpText: {
      fontWeight: "700",
      fontSize: 32,
      color: "#fff",
      position: "absolute",
      zIndex: 15,
   },
   lessonTitle: {
      textAlign: "center",
      fontWeight: 700,
      fontSize: 24,
      color: "#242B35",
      marginHorizontal: "auto",
      marginTop: 22,
   },
   lessonSubtitle: {
      fontWeight: 500,
      textAlign: "center",
      fontSize: 16,
      color: "#8e8e93",
      marginHorizontal: "auto",
      marginTop: 10,
   },
   dictionaryLink: {
      color: "#00afca",
   },
   statContainer: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      marginHorizontal: "auto",
      width: "80%",
      marginTop: 55,
   },
   statBox: {
      width: 140,
      height: 80,
      borderRadius: 10,
      backgroundColor: "#fef3e2",
      justifyContent: "center",
   },
   statBoxTitle: {
      textAlign: "center",
      color: "#F7AD3A",
      fontWeight: 700,
      fontSize: 16,
      marginHorizontal: "auto",
      marginBottom: 10,
   },
   statContent: {
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      marginHorizontal: "auto",
   },
   statBoxValue: {
      color: "#F7AD3A",
      fontWeight: 500,
      fontSize: 16,
      marginLeft: 10,
   },
   statBoxAlt: {
      width: 140,
      height: 80,
      borderRadius: 10,
      backgroundColor: "#d9f3f7",
      justifyContent: "center",
   },
   statBoxTitleAlt: {
      color: "#00AFCA",
      textAlign: "center",
      fontWeight: 700,
      fontSize: 16,
      marginHorizontal: "auto",
      marginBottom: 10,
   },
   statBoxValueAlt: {
      color: "#00AFCA",
      fontWeight: 500,
      fontSize: 16,
      marginLeft: 10,
   },
});
