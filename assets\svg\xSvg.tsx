import Svg, { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Defs, Rect } from "react-native-svg";

const XSvg = ({ color }: { color?: string }) => (
   <Svg width="14" height="14" viewBox="0 0 14 14" fill="none">
      <Path
         d="M13.1705 2.5636C13.6098 2.12425 13.6098 1.41193 13.1705 0.972579C12.7311 0.533229 12.0188 0.533229 11.5794 0.972579L7.07155 5.48048L2.56365 0.972579C2.1243 0.53323 1.41197 0.533229 0.972623 0.972579C0.533273 1.41193 0.533274 2.12426 0.972623 2.56361L5.48052 7.07151L0.972609 11.5794C0.53326 12.0188 0.533259 12.7311 0.972609 13.1704C1.41196 13.6098 2.12429 13.6098 2.56363 13.1704L7.07155 8.66253L11.5795 13.1704C12.0188 13.6098 12.7311 13.6098 13.1705 13.1704C13.6098 12.7311 13.6098 12.0188 13.1705 11.5794L8.66257 7.07151L13.1705 2.5636Z"
         fill="#242B35"
      />
   </Svg>
);

export default XSvg;
