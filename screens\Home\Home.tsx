import React from "react";
import { Safe<PERSON>reaProvider, SafeAreaView } from "react-native-safe-area-context";

import CurrentLessonButton from "./CurrentLessonButton";
import HomeHeader from "./HomeHeader";
import HomeAdditionalNav from "./HomeAdditionalNav";
import HomeMainContent from "./HomeMainContent";
import { ImageBackground, ScrollView, StatusBar, View } from "react-native";
import { cloudbg } from "../../assets/image";

export default function Home() {
   return (
      <SafeAreaProvider style={{ backgroundColor: "#fff" }}>
         <ImageBackground source={cloudbg} style={{ flex: 1 }}>
            <StatusBar barStyle={"light-content"} />
            <HomeHeader />
            <ScrollView showsHorizontalScrollIndicator={false}>
               <HomeAdditionalNav />
               <CurrentLessonButton />
               <HomeMainContent />
            </ScrollView>
         </ImageBackground>
      </SafeAreaProvider>
   );
}
