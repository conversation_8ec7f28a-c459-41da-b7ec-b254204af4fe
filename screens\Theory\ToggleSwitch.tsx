import React, { useState, useEffect, useRef } from "react";
import { View, Text, Pressable, StyleSheet, Animated } from "react-native";

export default function ToggleSwitch() {
   const [active, setActive] = useState(true);
   const animatedValue = useRef(new Animated.Value(0)).current; // Используем useRef для хранения Animated.Value

   useEffect(() => {
      Animated.timing(animatedValue, {
         toValue: active ? 0 : 1,
         duration: 300,
         useNativeDriver: false,
      }).start();
   }, [active]);

   const leftInterpolate = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 120], // Перемещаем слайдер на 100 единиц по оси X
   });

   const toggleSwitch = () => {
      setActive((prev) => !prev);
   };

   return (
      <View style={styles.container}>
         <View style={styles.switchBackground}>
            <Animated.View
               style={[
                  styles.switchKnob,
                  {
                     left: leftInterpolate,
                  },
               ]}
            />
            <Pressable onPress={toggleSwitch} style={styles.optionLeft}>
               <Text style={[styles.text, active ? styles.textActive : null]}>
                  Изучено
               </Text>
            </Pressable>
            <Pressable onPress={toggleSwitch} style={styles.optionRight}>
               <Text style={[styles.text, !active ? styles.textActive : null]}>
                  Не изучено
               </Text>
            </Pressable>
         </View>
      </View>
   );
}

const styles = StyleSheet.create({
   container: {
      justifyContent: "center",
      alignItems: "center",
      marginTop: 50,
   },
   switchBackground: {
      width: 240,
      height: 27,
      borderRadius: 25,
      backgroundColor: "#e0e0e0",
      flexDirection: "row",
      position: "relative",
   },
   switchKnob: {
      position: "absolute",
      width: "50%",
      height: "100%",
      borderRadius: 25,
      backgroundColor: "#f7ad3a",
   },
   optionLeft: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      borderRadius: 25,
   },
   optionRight: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      borderRadius: 25,
   },
   text: {
      fontWeight: "700",
      fontSize: 14,
      color: "#242B35",
   },
   textActive: {
      color: "#fff",
   },
});
