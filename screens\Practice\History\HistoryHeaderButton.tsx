import React from "react";
import { TouchableOpacity, Text, StyleSheet, View } from "react-native";

interface ButtonProps {
   title: string;
   handlePress: () => void;
}

const HistoryHeaderButton: React.FC<ButtonProps> = ({ title, handlePress }) => {
   return (
      <TouchableOpacity onPress={handlePress} style={styles.buttonContainer}>
         <View
            style={{
               ...styles.container,
               backgroundColor: "#F7AD3A",
               shadowColor: "#D79227", // Цвет тени, активная и неактивная
               shadowOffset: { width: 0, height: 4 }, // Смещение тени
               shadowOpacity: 1, // Прозрачность тени
               shadowRadius: 0, // Радиус размытия
               elevation: 4, // Только для Android
            }}
         >
            <Text style={{ ...styles.title, color: "#fff" }}>{title}</Text>
         </View>
      </TouchableOpacity>
   );
};

const styles = StyleSheet.create({
   buttonContainer: {},
   container: {
      borderRadius: 11,
      width: 200,
      height: 28,
      justifyContent: "center",
      alignItems: "center",
   },
   title: {
      fontWeight: "700",
      color: "#242B35",
      fontSize: 10,
   },
});

export default HistoryHeaderButton;
