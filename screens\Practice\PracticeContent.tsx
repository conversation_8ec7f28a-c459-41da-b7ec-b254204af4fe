import { Image, StyleSheet, Text, Touchable, TouchableOpacity, View } from "react-native";
import React from "react";
import ListeningSvg from "../../assets/svg/ListeningSvg";
import CrosswordSvg from "../../assets/svg/CrosswordSvg";
import HistorySvg from "../../assets/svg/HistorySvg";
import { useNavigation } from "@react-navigation/native";

export default function PracticeContent() {
   const { navigate } = useNavigation();
   return (
      <View style={{ flex: 1, backgroundColor: "#fff" }}>
         <Text
            style={{
               fontWeight: 600,
               fontSize: 20,
               paddingHorizontal: 15,
               paddingTop: 20,
               color: "#242B35",
            }}
         >
            Ваша коллекция
         </Text>

         <View
            style={{
               display: "flex",
               flexDirection: "row",
               paddingHorizontal: 15,
               paddingTop: 20,
            }}
         >
            <CollectionButton title="5 слов" />
            <CollectionButton title="2 правила" />
         </View>

         <Text
            style={{
               fontWeight: 600,
               fontSize: 20,
               paddingHorizontal: 15,
               paddingTop: 20,
               color: "#242B35",
               marginBottom: 20,
            }}
         >
            Тренировка
         </Text>

         <PracticeNavButton
            title="Аудирование"
            handlePress={() => {
               // @ts-ignore
               navigate("Listening");
            }}
            image={<ListeningSvg />}
         />
         <PracticeNavButton
            title="Кроссворд"
            handlePress={() => {
               // @ts-ignore
               navigate("Crossword");
            }}
            image={<CrosswordSvg />}
         />
         <PracticeNavButton
            title="Истории"
            handlePress={() => {
               // @ts-ignore
               navigate("History");
            }}
            image={<HistorySvg />}
         />
      </View>
   );
}

const PracticeNavButton = ({ title, image, handlePress }: { title: string; image: any; handlePress: () => void }) => {
   return (
      <TouchableOpacity
         onPress={handlePress}
         style={{
            width: "90%",
            height: 57,
            marginBottom: 19,
            marginHorizontal: "auto",
            backgroundColor: "#fff",
            borderRadius: 8,
            borderWidth: 1.21,
            borderColor: "#E1E1E1",
            // Тень для iOS
            shadowColor: "#8F8F91", // Цвет тени
            shadowOffset: {
               width: 0,
               height: 2.43, // Смещение по оси Y
            },
            shadowOpacity: 0.25, // Прозрачность тени (0.25 соответствует "40" в шестнадцатеричном формате)
            shadowRadius: 0, // Радиус размытия тени
            // Тень для Android
            elevation: 3, // Примерная эквивалентность для Android
         }}
      >
         <View
            style={{
               marginHorizontal: 15,
               alignItems: "center",
               marginVertical: "auto",
               display: "flex",
               flexDirection: "row",
               justifyContent: "space-between",
            }}
         >
            <Text style={{ color: "#242B35", fontWeight: 700, fontSize: 17 }}>{title}</Text>

            {image}
         </View>
      </TouchableOpacity>
   );
};

const CollectionButton = ({ title }: { title: string }) => {
   return (
      <TouchableOpacity
         style={{
            height: 58,
            backgroundColor: "#fff",
            borderRadius: 8,
            borderColor: "#E1E1E1",
            borderWidth: 1.21,
            marginRight: 30,
            shadowColor: "#8F8F91", // Цвет тени
            shadowOffset: {
               width: 0,
               height: 2.43, // Смещение по оси Y (как в box-shadow)
            },
            shadowOpacity: 0.25, // Прозрачность тени (0.25 соответствует "40" в шестнадцатеричном формате)
            shadowRadius: 0, // Радиус размытия
            elevation: 3, // Тень для Android
         }}
      >
         <Text
            style={{
               color: "#242B35",
               fontWeight: "700",
               fontSize: 17,
               margin: "auto",
               paddingHorizontal: 30,
            }}
         >
            {title}
         </Text>
      </TouchableOpacity>
   );
};
