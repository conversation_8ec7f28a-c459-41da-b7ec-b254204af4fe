import { StyleSheet, Text, View } from "react-native";
import React from "react";
import LevelOrnamentSvg from "../../assets/svg/LevelOrnamentSvg";
import Animated, { useSharedValue, useAnimatedStyle, withRepeat, withTiming, Easing } from "react-native-reanimated";
import getRandomProverb from "../../utils/GenerateProverbs";

export default function LessonLoading({ type }: { type: string }) {
   // Создаем анимацию для движения вверх и вниз
   const translationY1 = useSharedValue(0);
   const translationY2 = useSharedValue(0);
   const translationY3 = useSharedValue(0);

   // Анимация для первого орнамента
   const ornamentStyle1 = useAnimatedStyle(() => {
      return {
         transform: [{ translateY: translationY1.value }],
      };
   });

   // Анимация для второго орнамента
   const ornamentStyle2 = useAnimatedStyle(() => {
      return {
         transform: [{ translateY: translationY2.value }],
      };
   });

   // Анимация для третьего орнамента
   const ornamentStyle3 = useAnimatedStyle(() => {
      return {
         transform: [{ translateY: translationY3.value }],
      };
   });

   // Запуск анимаций при монтировании компонента
   React.useEffect(() => {
      translationY1.value = withRepeat(
         withTiming(-20, {
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
         }),
         -1,
         true
      );

      translationY2.value = withRepeat(
         withTiming(-20, {
            duration: 1200,
            easing: Easing.inOut(Easing.ease),
         }),
         -1,
         true
      );

      translationY3.value = withRepeat(
         withTiming(-20, {
            duration: 1400,
            easing: Easing.inOut(Easing.ease),
         }),
         -1,
         true
      );
   }, []);

   let title = "Новые слова";

   if (type == "grammar") {
      title = "Грамматика";
   }
   if (type == "practice") {
      title = "Практика";
   }
   if (type == "done") {
      title = "Вы прошли уровень!";
   }

   return (
      <View style={{ height: "100%" }}>
         <View
            style={{
               marginVertical: "auto",
               display: "flex",
               flexDirection: "column",
               justifyContent: "space-between",
               height: "44%",
            }}
         >
            <View>
               <View
                  style={{
                     display: "flex",
                     flexDirection: "row",
                     justifyContent: "space-between",
                     width: 330,
                     marginHorizontal: "auto",
                  }}
               >
                  {/* Первый орнамент */}
                  <Animated.View style={[{ transform: [{ rotate: "-30deg" }] }, ornamentStyle1]}>
                     <LevelOrnamentSvg
                        size="97"
                        color={type == "grammar" || type == "practice" || type == "done" ? "#F7AD3A" : "#E1E1E1"}
                     />
                  </Animated.View>

                  {/* Второй орнамент */}
                  <Animated.View style={ornamentStyle2}>
                     <LevelOrnamentSvg size="97" color={type == "practice" || type == "done" ? "#F7AD3A" : "#E1E1E1"} />
                  </Animated.View>

                  {/* Третий орнамент */}
                  <Animated.View style={[{ transform: [{ rotate: "30deg" }] }, ornamentStyle3]}>
                     <LevelOrnamentSvg color={type == "done" ? "#F7AD3A" : "#E1E1E1"} size="97" />
                  </Animated.View>
               </View>

               <Text
                  style={{
                     color: "#04212F",
                     fontWeight: 700,
                     fontSize: 24,
                     marginHorizontal: "auto",
                     textAlign: "center",
                     paddingTop: 50,
                     paddingBottom: 25,
                  }}
               >
                  {title}
               </Text>

               <Text
                  style={{
                     color: "#8F8F91",
                     fontWeight: 500,
                     fontSize: 16,
                     marginHorizontal: "auto",
                     textAlign: "center",
                     width: "88%",
                     lineHeight: 20,
                  }}
               >
                  {getRandomProverb()}
               </Text>
            </View>
         </View>
      </View>
   );
}
