// axiosInstance.ts
import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { jwtDecode, JwtPayload } from "jwt-decode";

// Конфигурация сервера
const prod = "https://narxoz-sporthub.online";
const local = "http://172.20.10.2:8080";

export const baseUrl = local; // Локальный или продакшн URL

const axiosInstance = axios.create({
   baseURL: baseUrl,
   headers: {
      "Content-Type": "application/json",
   },
});

// Функция проверки истечения токена
const isTokenExpired = (token: string): boolean => {
   try {
      const { exp } = jwtDecode<JwtPayload>(token);
      return exp ? Date.now() >= exp * 1000 : true;
   } catch (error) {
      console.error("Ошибка декодирования токена:", error);
      return true;
   }
};

// Функция настройки интерцепторов
let isRefreshing = false;
let refreshPromise: Promise<any> | null = null;

export const setupInterceptors = (logout: () => Promise<void>) => {
   axiosInstance.interceptors.request.use(
      async (config) => {
         const accessToken = await AsyncStorage.getItem("accessToken");

         if (accessToken && !isTokenExpired(accessToken)) {
            config.headers.Authorization = `Bearer ${accessToken}`;
         }
         return config;
      },
      (error) => Promise.reject(error)
   );

   axiosInstance.interceptors.response.use(
      (response) => response,
      async (error) => {
         const originalRequest = error.config;

         if (error.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            // Если обновление токена уже выполняется, ждем его завершения
            if (isRefreshing) {
               console.log("Ожидаем завершения обновления токена...");
               try {
                  await refreshPromise;
                  return axiosInstance(originalRequest);
               } catch (refreshError) {
                  return Promise.reject(refreshError);
               }
            }

            isRefreshing = true;
            refreshPromise = (async () => {
               const refreshToken = await AsyncStorage.getItem("refreshToken");
               console.log("Отправленный рефреш токен:", refreshToken);

               if (refreshToken && !isTokenExpired(refreshToken)) {
                  try {
                     const { data } = await axios.post(`${baseUrl}/v1/users/refresh`, {
                        refresh_token: refreshToken,
                     });

                     await AsyncStorage.setItem("accessToken", data.tokens.access_token);
                     await AsyncStorage.setItem("refreshToken", data.tokens.refresh_token);

                     console.log("ЗАПИСАННЫЙ рефреш токен:", data.tokens.refresh_token);
                     axiosInstance.defaults.headers.Authorization = `Bearer ${data.tokens.access_token}`;

                     return data;
                  } catch (refreshError) {
                     console.error("Ошибка обновления токена:", refreshError);
                     await logout();
                     throw refreshError;
                  } finally {
                     isRefreshing = false;
                     refreshPromise = null;
                  }
               } else {
                  isRefreshing = false;
                  await logout();
                  throw new Error("Refresh token expired or missing.");
               }
            })();

            try {
               await refreshPromise;
               return axiosInstance(originalRequest);
            } catch (refreshError) {
               return Promise.reject(refreshError);
            }
         }

         return Promise.reject(error);
      }
   );
};

export const PostRequest = async (url: string, body: any) => {
   try {
      const response = await axiosInstance.post(url, body);
      return response.data;
   } catch (error) {
      throw error;
   }
};

export const PutRequest = async (url: string, body: any) => {
   try {
      const response = await axiosInstance.put(url, body);
      return response.data;
   } catch (error) {
      throw error;
   }
};

export const GetRequest = async (url: string) => {
   try {
      const response = await axiosInstance.get(url);
      return response.data;
   } catch (error) {
      throw error;
   }
};

// ===== PUSH NOTIFICATIONS API V2.0 =====

// Типы для регистрации устройства
export interface DeviceTokenRequest {
   token: string;
   device_type: 'ios' | 'android' | 'web';
   device_id?: string;
   device_name?: string;
   app_version?: string;
}

export interface DeviceTokenResponse {
   status: string;
   message: string;
   device_token?: {
      id: number;
      user_id: number;
      token: string;
      device_type: string;
      device_id?: string;
      device_name?: string;
      app_version?: string;
      is_active: boolean;
      last_used_at: string;
      created_at: string;
      updated_at: string;
   };
}

// Типы для отправки уведомлений
export interface SendNotificationRequest {
   user_id: number;
   type: 'achievement' | 'reminder' | 'new_content' | 'progress' | 'welcome' | 'streak_lost' | 'streak_milestone' | 'announcement';
   title: string;
   body: string;
   image_url?: string;
   data?: any;
   send_at?: string; // RFC3339 format
}

export interface BulkNotificationRequest {
   user_ids: number[];
   type: 'achievement' | 'reminder' | 'new_content' | 'progress' | 'welcome' | 'streak_lost' | 'streak_milestone' | 'announcement';
   title: string;
   body: string;
   image_url?: string;
   data?: any;
   send_at?: string;
}

export interface GroupNotificationRequest {
   group_type: 'all' | 'role' | 'level' | 'active' | 'inactive';
   group_value?: string; // для role и level
   type: 'achievement' | 'reminder' | 'new_content' | 'progress' | 'welcome' | 'streak_lost' | 'streak_milestone' | 'announcement';
   title: string;
   body: string;
   image_url?: string;
   data?: any;
   send_at?: string;
}

export interface AdminNotificationRequest {
   target_type: 'user' | 'bulk' | 'group' | 'all';
   user_ids?: number[]; // для user и bulk
   group_type?: string; // для group
   group_value?: string;
   type: 'achievement' | 'reminder' | 'new_content' | 'progress' | 'welcome' | 'streak_lost' | 'streak_milestone' | 'announcement';
   title: string;
   body: string;
   image_url?: string;
   data?: any;
   send_at?: string;
}

export interface NotificationResponse {
   status: string;
   message: string;
   stats?: {
      total_sent: number;
      total_success: number;
      total_failed: number;
      success_rate: number;
      service_used: string;
      sent_at: string;
   };
   admin_user_id?: number;
}

// Типы для настроек уведомлений
export interface NotificationSettingsResponse {
   notification_settings: {
      id: number;
      user_id: number;
      push_enabled: boolean;
      achievements_enabled: boolean;
      reminders_enabled: boolean;
      new_content_enabled: boolean;
      daily_reminder_time: string;
      timezone: string;
      created_at: string;
      updated_at: string;
   };
}

export interface NotificationSettingsRequest {
   push_enabled?: boolean;
   achievements_enabled?: boolean;
   reminders_enabled?: boolean;
   new_content_enabled?: boolean;
   daily_reminder_time?: string;
   timezone?: string;
}

// Типы для истории уведомлений
export interface NotificationHistoryResponse {
   notifications: Array<{
      id: number;
      user_id: number;
      type: string;
      title: string;
      body: string;
      data?: any;
      sent_at: string;
      delivered_at?: string;
      opened_at?: string;
      status: string;
   }>;
   pagination?: {
      page: number;
      limit: number;
      total: number;
      total_pages: number;
   };
}

// Типы для статистики
export interface NotificationStatsResponse {
   stats: {
      total_sent: number;
      total_success: number;
      total_failed: number;
      success_rate: number;
      by_type: Record<string, number>;
      by_service: Record<string, number>;
   };
}

// API функции для уведомлений v2.0

/**
 * Регистрация токена устройства
 */
export const registerDeviceToken = async (tokenData: DeviceTokenRequest): Promise<DeviceTokenResponse> => {
   try {
      const response = await axiosInstance.post('/v1/notifications/device-token', tokenData);
      return response.data;
   } catch (error) {
      console.error('Error registering device token:', error);
      throw error;
   }
};

/**
 * Отправка индивидуального уведомления
 */
export const sendNotification = async (notificationData: SendNotificationRequest): Promise<NotificationResponse> => {
   try {
      const response = await axiosInstance.post('/v1/notifications/send', notificationData);
      return response.data;
   } catch (error) {
      console.error('Error sending notification:', error);
      throw error;
   }
};

/**
 * Отправка массовых уведомлений
 */
export const sendBulkNotification = async (notificationData: BulkNotificationRequest): Promise<NotificationResponse> => {
   try {
      const response = await axiosInstance.post('/v1/notifications/bulk', notificationData);
      return response.data;
   } catch (error) {
      console.error('Error sending bulk notification:', error);
      throw error;
   }
};

/**
 * Отправка групповых уведомлений
 */
export const sendGroupNotification = async (notificationData: GroupNotificationRequest): Promise<NotificationResponse> => {
   try {
      const response = await axiosInstance.post('/v1/notifications/group', notificationData);
      return response.data;
   } catch (error) {
      console.error('Error sending group notification:', error);
      throw error;
   }
};

/**
 * Отправка админских уведомлений
 */
export const sendAdminNotification = async (notificationData: AdminNotificationRequest): Promise<NotificationResponse> => {
   try {
      const response = await axiosInstance.post('/v1/notifications/admin', notificationData);
      return response.data;
   } catch (error) {
      console.error('Error sending admin notification:', error);
      throw error;
   }
};

/**
 * Получение настроек уведомлений
 */
export const getNotificationSettings = async (): Promise<NotificationSettingsResponse> => {
   try {
      const response = await axiosInstance.get('/v1/notifications/settings');
      return response.data;
   } catch (error) {
      console.error('Error getting notification settings:', error);
      throw error;
   }
};

/**
 * Обновление настроек уведомлений
 */
export const updateNotificationSettings = async (settings: NotificationSettingsRequest): Promise<NotificationSettingsResponse> => {
   try {
      const response = await axiosInstance.put('/v1/notifications/settings', settings);
      return response.data;
   } catch (error) {
      console.error('Error updating notification settings:', error);
      throw error;
   }
};

/**
 * Получение истории уведомлений
 */
export const getNotificationHistory = async (page: number = 1, limit: number = 20): Promise<NotificationHistoryResponse> => {
   try {
      const response = await axiosInstance.get(`/v1/notifications/history?page=${page}&limit=${limit}`);
      return response.data;
   } catch (error) {
      console.error('Error getting notification history:', error);
      throw error;
   }
};

/**
 * Получение статистики уведомлений (только для администраторов)
 */
export const getNotificationStats = async (period: string = '7d', type?: string): Promise<NotificationStatsResponse> => {
   try {
      const params = new URLSearchParams({ period });
      if (type) params.append('type', type);

      const response = await axiosInstance.get(`/v1/notifications/stats?${params.toString()}`);
      return response.data;
   } catch (error) {
      console.error('Error getting notification stats:', error);
      throw error;
   }
};

/**
 * Отправка тестового уведомления
 */
export const sendTestNotification = async (testData: {
   type: string;
   title: string;
   body: string;
   image_url?: string;
   data?: any;
}): Promise<NotificationResponse> => {
   try {
      const response = await axiosInstance.post('/v1/notifications/test', testData);
      return response.data;
   } catch (error) {
      console.error('Error sending test notification:', error);
      throw error;
   }
};

/**
 * Деактивация токена устройства (для совместимости)
 */
export const deactivateDeviceToken = async (token: string): Promise<{ message: string }> => {
   try {
      const response = await axiosInstance.delete('/v1/notifications/device-token', {
         data: { token }
      });
      return response.data;
   } catch (error) {
      console.error('Error deactivating device token:', error);
      throw error;
   }
};

export default axiosInstance;
