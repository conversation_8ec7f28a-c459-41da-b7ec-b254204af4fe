import { Image, StyleSheet, Text, View } from "react-native";
import React from "react";

interface AchievementCardProps {
   image: any;
   title: string;
   maxCount: number;
   currentCount: number;
   isPassed?: boolean;
   bgColor: string;
   textColor: string;
   reward?: number;
   icon?: string;
}

export const AchievementCard: React.FC<AchievementCardProps> = ({
   image,
   title,
   maxCount,
   currentCount,
   isPassed,
   bgColor,
   textColor,
   reward,
   icon,
}) => {
   const progressPercentage = maxCount > 0 ? (currentCount / maxCount) * 100 : 0;
   const isCompleted = isPassed || currentCount >= maxCount;

   return (
      <View
         style={{
            width: 132,
            height: 200,
            marginRight: 17,
            borderRadius: 15,
            borderWidth: 0.5,
            borderColor: isCompleted ? "#4CAF50" : "#E1E1E1",
            backgroundColor: isCompleted ? "#F8F8F8" : "#fff",
            shadowColor: "#E1E1E1",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.3,
            shadowRadius: 10,
            elevation: 6,
            position: "relative",
            opacity: isCompleted ? 1 : 0.8,
         }}
      >
         {/* Иконка достижения или изображение */}
         <View style={{ alignItems: "center", marginTop: 10 }}>
            {icon ? (
               <Text style={{ fontSize: 40, marginBottom: 5 }}>{icon}</Text>
            ) : (
               <Image
                  style={{
                     width: 60,
                     height: 60,
                     alignSelf: "center",
                  }}
                  source={image}
               />
            )}
         </View>

         {/* Индикатор завершения */}
         {isCompleted && (
            <View
               style={{
                  position: "absolute",
                  top: 8,
                  right: 8,
                  backgroundColor: "#4CAF50",
                  borderRadius: 10,
                  width: 20,
                  height: 20,
                  justifyContent: "center",
                  alignItems: "center",
               }}
            >
               <Text style={{ color: "#fff", fontSize: 12, fontWeight: "bold" }}>✓</Text>
            </View>
         )}

         {/* Прогресс бар */}
         <View
            style={{
               marginHorizontal: 10,
               marginTop: 8,
               height: 4,
               backgroundColor: "#E1E1E1",
               borderRadius: 2,
            }}
         >
            <View
               style={{
                  height: 4,
                  backgroundColor: isCompleted ? "#4CAF50" : bgColor,
                  borderRadius: 2,
                  width: `${Math.min(progressPercentage, 100)}%`,
               }}
            />
         </View>

         {/* Счетчик прогресса */}
         <View
            style={{
               position: "absolute",
               bottom: 35,
               alignSelf: "center",
               backgroundColor: bgColor,
               paddingHorizontal: 8,
               paddingVertical: 2,
               borderRadius: 10,
               minWidth: 36,
               justifyContent: "center",
               alignItems: "center",
            }}
         >
            <Text style={{ color: textColor, fontWeight: "700", fontSize: 12 }}>
               {currentCount}/{maxCount}
            </Text>
         </View>

         {/* Название достижения */}
         <View style={{ paddingHorizontal: 8, marginTop: 5 }}>
            <Text
               style={{
                  color: "#242B35",
                  fontWeight: "600",
                  fontSize: 12,
                  textAlign: "center",
                  lineHeight: 14,
               }}
               numberOfLines={2}
            >
               {title}
            </Text>
         </View>

         {/* Награда */}
         {reward && reward > 0 && (
            <View
               style={{
                  position: "absolute",
                  bottom: 8,
                  alignSelf: "center",
                  backgroundColor: "#FFD700",
                  paddingHorizontal: 6,
                  paddingVertical: 2,
                  borderRadius: 8,
               }}
            >
               <Text style={{ color: "#B8860B", fontWeight: "600", fontSize: 10 }}>
                  +{reward} XP
               </Text>
            </View>
         )}
      </View>
   );
};

const styles = StyleSheet.create({});
