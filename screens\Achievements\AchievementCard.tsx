import { Image, StyleSheet, Text, View } from "react-native";
import React from "react";

interface AchievementCardProps {
   image: any;
   title: string;
   maxCount: number;
   currentCount: number;
   isPassed?: boolean;
   bgColor: string;
   textColor: string;
}

export const AchievementCard: React.FC<AchievementCardProps> = ({
   image,
   title,
   maxCount,
   currentCount,
   isPassed,
   bgColor,
   textColor,
}) => {
   return (
      <View
         style={{
            width: 132,
            height: 176,
            marginRight: 17,
            borderRadius: 15,
            borderWidth: 0.5,
            borderColor: "#E1E1E1",
            backgroundColor: "#fff",
            shadowColor: "#E1E1E1",
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.3,
            shadowRadius: 10,
            elevation: 6,
            position: "relative", // Это важно для позиционирования желтого круга
         }}
      >
         <View>
            <Image
               style={{
                  width: 100,
                  height: 100,
                  alignSelf: "center",
                  marginTop: 10,
               }}
               source={image} // Ваше изображение стрелы
            />

            {/* Желтый круг с цифрой */}
            <View
               style={{
                  position: "absolute",
                  bottom: -8, // Позиционируем его внизу
                  alignSelf: "center",
                  backgroundColor: bgColor, // Желтый фон
                  width: 36,
                  height: 20,
                  borderRadius: 41,
                  justifyContent: "center",
                  alignItems: "center",
               }}
            >
               <Text style={{ color: textColor, fontWeight: 700, fontSize: 14 }}>{maxCount}</Text>
            </View>
         </View>

         <Text
            style={{
               fontWeight: 600,
               fontSize: 16,
               marginHorizontal: "auto",
               color: "#242B35",
               textAlign: "center",
               marginTop: 14,
            }}
         >
            {title}
         </Text>
         <Text
            style={{
               fontWeight: 500,
               fontSize: 14,
               marginHorizontal: "auto",
               textAlign: "center",
               color: "#8E8E93",
               marginTop: 5,
            }}
         >
            {currentCount} из {maxCount}
         </Text>
      </View>
   );
};

const styles = StyleSheet.create({});
