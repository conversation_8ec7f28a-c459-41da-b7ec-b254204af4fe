import Svg, { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Defs, Rect } from "react-native-svg";

const BookSvg = () => (
   <Svg width="41" height="41" viewBox="0 0 41 41" fill="none">
      <Path
         d="M5.125 13.6666C5.125 8.83473 5.125 6.41878 6.62608 4.9177C8.12716 3.41663 10.5431 3.41663 15.375 3.41663H25.625C30.4569 3.41663 32.8728 3.41663 34.3739 4.9177C35.875 6.41878 35.875 8.83473 35.875 13.6666V27.3333C35.875 32.1652 35.875 34.5811 34.3739 36.0822C32.8728 37.5833 30.4569 37.5833 25.625 37.5833H15.375C10.5431 37.5833 8.12716 37.5833 6.62608 36.0822C5.125 34.5811 5.125 32.1652 5.125 27.3333V13.6666Z"
         fill="#FFFCDE"
      />
      <Path
         fill-rule="evenodd"
         clipRule="evenodd"
         d="M14.948 3.43787V37.6026H12.3855V3.43787H14.948Z"
         fill="#F7AD3A"
      />
      <Path
         fill-rule="evenodd"
         clipRule="evenodd"
         d="M2.1355 13.6666C2.1355 12.959 2.70913 12.3854 3.41675 12.3854H6.83341C7.54103 12.3854 8.11466 12.959 8.11466 13.6666C8.11466 14.3742 7.54103 14.9479 6.83341 14.9479H3.41675C2.70913 14.9479 2.1355 14.3742 2.1355 13.6666ZM2.1355 20.5C2.1355 19.7923 2.70913 19.2187 3.41675 19.2187H6.83341C7.54103 19.2187 8.11466 19.7923 8.11466 20.5C8.11466 21.2076 7.54103 21.7812 6.83341 21.7812H3.41675C2.70913 21.7812 2.1355 21.2076 2.1355 20.5ZM2.1355 27.3333C2.1355 26.6257 2.70913 26.052 3.41675 26.052H6.83341C7.54103 26.052 8.11466 26.6257 8.11466 27.3333C8.11466 28.0409 7.54103 28.6145 6.83341 28.6145H3.41675C2.70913 28.6145 2.1355 28.0409 2.1355 27.3333Z"
         fill="#242B35"
      />
      <Path
         d="M18.3645 11.1041C18.3645 10.3965 18.9381 9.82288 19.6458 9.82288H28.1874C28.895 9.82288 29.4687 10.3965 29.4687 11.1041C29.4687 11.8117 28.895 12.3854 28.1874 12.3854H19.6458C18.9381 12.3854 18.3645 11.8117 18.3645 11.1041Z"
         fill="#242B35"
      />
      <Path
         d="M18.3645 17.0833C18.3645 16.3757 18.9381 15.802 19.6458 15.802H28.1874C28.895 15.802 29.4687 16.3757 29.4687 17.0833C29.4687 17.7909 28.895 18.3645 28.1874 18.3645H19.6458C18.9381 18.3645 18.3645 17.7909 18.3645 17.0833Z"
         fill="#242B35"
      />
   </Svg>
);

export default BookSvg;
