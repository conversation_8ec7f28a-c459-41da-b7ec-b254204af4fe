import { Image, StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React from "react";
import { Cool, <PERSON> } from "../../assets/image";

export default function First({ handlePress }: { handlePress: () => void }) {
   return (
      <View>
         <TouchableOpacity onPress={handlePress} style={styles.card}>
            <View style={styles.content}>
               <Image style={styles.image} source={Smile} />
               <Text style={styles.mainText}>Изучаете впервые казахский?</Text>
               <Text style={styles.subText}>Научим с нуля!</Text>
            </View>
         </TouchableOpacity>

         <TouchableOpacity
            onPress={handlePress}
            style={[styles.card, styles.cardMargin]}
         >
            <View style={styles.content}>
               <Image style={styles.image} source={Cool} />
               <Text style={styles.mainText}>
                  Уже немного знаете казахский?
               </Text>
               <Text style={styles.subText}>Проверьте свой уровень!</Text>
            </View>
         </TouchableOpacity>
      </View>
   );
}

const styles = StyleSheet.create({
   card: {
      width: 346,
      height: 175,
      borderWidth: 1,
      borderColor: "rgba(143, 143, 145, 0.5)",
      borderTopRightRadius: 16,
      borderBottomLeftRadius: 16,
      borderTopLeftRadius: 2,
      borderBottomRightRadius: 2,
      backgroundColor: "#fff",
      shadowColor: "rgba(143, 143, 145, 0.5)",
      shadowOffset: {
         width: 0,
         height: 0,
      },
      shadowOpacity: 0.2,
      shadowRadius: 12,
      elevation: 4, // Только для Android
      marginHorizontal: "auto", // Не поддерживается в React Native, можно использовать alignSelf
   },
   cardMargin: {
      marginTop: 20,
   },
   content: {
      flex: 1,
      alignItems: "center",
      marginTop: 10,
   },
   image: {
      marginTop: 0,
   },
   mainText: {
      fontWeight: "600",
      fontSize: 16,
      lineHeight: 20,
      color: "#00AFCA",
   },
   subText: {
      fontWeight: "700",
      fontSize: 12,
      lineHeight: 15,
      color: "#8F8F91",
      marginTop: 10,
   },
});
