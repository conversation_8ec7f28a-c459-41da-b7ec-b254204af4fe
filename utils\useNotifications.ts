import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import NotificationService from './NotificationService';
import {
  registerDeviceToken,
  getNotificationSettings,
  updateNotificationSettings,
  getNotificationHistory,
  deactivateDeviceToken,
  sendTestNotification,
  sendNotification,
  getNotificationStats,
  NotificationSettingsRequest,
  NotificationSettingsResponse,
  NotificationHistoryResponse,
  NotificationStatsResponse,
  SendNotificationRequest,
} from './service';

// Хук для управления Push уведомлениями
export const useNotifications = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [token, setToken] = useState<string | null>(null);
  const [tokenType, setTokenType] = useState<'fcm' | 'expo' | null>(null);
  const [permissionStatus, setPermissionStatus] = useState<string>('undetermined');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Инициализация уведомлений
  const initialize = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      await NotificationService.initialize();
      const currentToken = NotificationService.getToken();
      const currentTokenType = NotificationService.getTokenType();
      const status = await NotificationService.getPermissionStatus();

      setToken(currentToken);
      setTokenType(currentTokenType);
      setPermissionStatus(status);
      setIsInitialized(true);

      // Если токен получен, регистрируем его на сервере
      if (currentToken) {
        await registerTokenOnServer(currentToken);
      }
    } catch (err) {
      console.error('Error initializing notifications:', err);
      setError('Failed to initialize notifications');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Регистрация токена на сервере
  const registerTokenOnServer = useCallback(async (pushToken: string) => {
    try {
      const deviceInfo = await NotificationService.getDeviceInfo();
      
      await registerDeviceToken({
        token: pushToken,
        ...deviceInfo,
      });

      console.log('Device token registered successfully');
    } catch (err) {
      console.error('Error registering token on server:', err);
      // Не показываем ошибку пользователю, так как это фоновая операция
    }
  }, []);

  // Запрос разрешений
  const requestPermissions = useCallback(async () => {
    try {
      setIsLoading(true);
      const granted = await NotificationService.requestPermissions();
      
      if (granted) {
        await initialize();
        return true;
      } else {
        Alert.alert(
          'Разрешения не предоставлены',
          'Для получения уведомлений необходимо разрешить их в настройках приложения.',
          [{ text: 'OK' }]
        );
        return false;
      }
    } catch (err) {
      console.error('Error requesting permissions:', err);
      setError('Failed to request permissions');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [initialize]);

  // Деактивация токена
  const deactivateToken = useCallback(async () => {
    try {
      if (token) {
        await deactivateDeviceToken(token);
        setToken(null);
        console.log('Token deactivated successfully');
      }
    } catch (err) {
      console.error('Error deactivating token:', err);
    }
  }, [token]);

  // Отправка тестового уведомления
  const sendTest = useCallback(async (title: string, body: string, type: string = 'test') => {
    try {
      const response = await sendTestNotification({
        type,
        title,
        body,
      });

      if (response.status === 'success') {
        Alert.alert('Успех', `Тестовое уведомление отправлено через ${response.stats?.service_used}`);
      } else {
        Alert.alert('Ошибка', response.message);
      }
    } catch (err) {
      console.error('Error sending test notification:', err);
      Alert.alert('Ошибка', 'Не удалось отправить тестовое уведомление');
    }
  }, []);

  // Отправка персонального уведомления
  const sendPersonalNotification = useCallback(async (notificationData: SendNotificationRequest) => {
    try {
      const response = await sendNotification(notificationData);
      return response;
    } catch (err) {
      console.error('Error sending personal notification:', err);
      throw err;
    }
  }, []);

  // Очистка уведомлений
  const clearNotifications = useCallback(async () => {
    try {
      await NotificationService.clearAllNotifications();
      await NotificationService.setBadgeCount(0);
    } catch (err) {
      console.error('Error clearing notifications:', err);
    }
  }, []);

  useEffect(() => {
    // Автоматическая инициализация при монтировании
    initialize();

    // Очистка при размонтировании
    return () => {
      NotificationService.cleanup();
    };
  }, [initialize]);

  return {
    isInitialized,
    token,
    tokenType,
    permissionStatus,
    isLoading,
    error,
    initialize,
    requestPermissions,
    deactivateToken,
    sendTest,
    sendPersonalNotification,
    clearNotifications,
  };
};

// Хук для управления настройками уведомлений
export const useNotificationSettings = () => {
  const [settings, setSettings] = useState<NotificationSettingsResponse['notification_settings'] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Загрузка настроек
  const loadSettings = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await getNotificationSettings();
      setSettings(response.notification_settings);
    } catch (err) {
      console.error('Error loading notification settings:', err);
      setError('Failed to load settings');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Обновление настроек
  const updateSettings = useCallback(async (newSettings: NotificationSettingsRequest) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await updateNotificationSettings(newSettings);
      setSettings(response.notification_settings);
      
      return true;
    } catch (err) {
      console.error('Error updating notification settings:', err);
      setError('Failed to update settings');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  return {
    settings,
    isLoading,
    error,
    loadSettings,
    updateSettings,
  };
};

// Хук для истории уведомлений
export const useNotificationHistory = () => {
  const [history, setHistory] = useState<NotificationHistoryResponse['notifications']>([]);
  const [pagination, setPagination] = useState<NotificationHistoryResponse['pagination'] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Загрузка истории
  const loadHistory = useCallback(async (page: number = 1, limit: number = 20) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await getNotificationHistory(page, limit);
      
      if (page === 1) {
        setHistory(response.notifications);
      } else {
        setHistory(prev => [...prev, ...response.notifications]);
      }
      
      setPagination(response.pagination);
    } catch (err) {
      console.error('Error loading notification history:', err);
      setError('Failed to load history');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Загрузка следующей страницы
  const loadMore = useCallback(async () => {
    if (pagination && pagination.page < pagination.total_pages && !isLoading) {
      await loadHistory(pagination.page + 1);
    }
  }, [pagination, isLoading, loadHistory]);

  useEffect(() => {
    loadHistory();
  }, [loadHistory]);

  return {
    history,
    pagination,
    isLoading,
    error,
    loadHistory,
    loadMore,
    hasMore: pagination ? pagination.page < pagination.total_pages : false,
  };
};

// Хук для статистики уведомлений (для администраторов)
export const useNotificationStats = () => {
  const [stats, setStats] = useState<NotificationStatsResponse['stats'] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Загрузка статистики
  const loadStats = useCallback(async (period: string = '7d', type?: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await getNotificationStats(period, type);
      setStats(response.stats);
    } catch (err) {
      console.error('Error loading notification stats:', err);
      setError('Failed to load stats');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadStats();
  }, [loadStats]);

  return {
    stats,
    isLoading,
    error,
    loadStats,
  };
};
