import Svg, { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Defs, Rect } from "react-native-svg";

const ReportSvg = ({ color }: { color?: string }) => (
   <Svg width="16" height="18" viewBox="0 0 16 18" fill="none">
      <Path
         fillRule="evenodd"
         clipRule="evenodd"
         d="M8.71394 1.61489C6.90469 0.34005 4.78079 0.37978 3.26456 0.660936C2.34006 0.832361 1.40058 1.11257 0.57584 1.52693C0.219749 1.70616 0 2.03968 0 2.40092V16.4934C0 17.0493 0.51168 17.5 1.14286 17.5C1.77403 17.5 2.28571 17.0493 2.28571 16.4934V11.0873C3.85848 10.5034 5.856 10.2322 7.28606 11.2398C9.09531 12.5146 11.2192 12.4749 12.7354 12.1938C13.8925 11.9792 14.7933 11.6135 15.1947 11.4349C15.6422 11.236 16 10.9303 16 10.4538V2.40092C16 2.04208 15.7831 1.71039 15.4307 1.53028C15.0786 1.3503 14.645 1.34862 14.2914 1.52631L14.2904 1.52679C12.647 2.3177 10.2872 2.72347 8.71394 1.61489ZM2.28571 3.03444V8.94284C4.43584 8.33666 6.88114 8.37632 8.71394 9.6677C9.76183 10.4061 11.0665 10.4459 12.2646 10.2237C12.8384 10.1172 13.345 9.9573 13.7143 9.8202V3.91187C13.416 3.99597 13.087 4.0757 12.7354 4.14088C11.2192 4.42204 9.09531 4.46177 7.28606 3.18693C5.856 2.17931 3.85848 2.45055 2.28571 3.03444Z"
         fill={!color ? "#00AFCA" : color}
      />
   </Svg>
);

export default ReportSvg;
