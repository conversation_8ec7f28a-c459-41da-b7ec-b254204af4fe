import { StyleSheet, Text, TextInput, TouchableOpacity, View } from "react-native";
import React from "react";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";
import XSvg from "../../assets/svg/xSvg";
import { useNavigation } from "@react-navigation/native";
import { useAuth } from "../../context/AuthContext";

export default function ProfileEdit() {
   const { navigate } = useNavigation();
   const { authState, logout } = useAuth();
   return (
      <SafeAreaProvider>
         <SafeAreaView style={{ backgroundColor: "#fff", height: "100%" }}>
            <View
               style={{
                  width: "90%",
                  marginHorizontal: "auto",
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
               }}
            >
               <TouchableOpacity
                  onPress={() => {
                     // @ts-ignore
                     navigate("Profile");
                  }}
                  style={{ position: "absolute" }}
               >
                  <XSvg />
               </TouchableOpacity>

               <Text
                  style={{
                     color: "#242B35",
                     fontWeight: 600,
                     fontSize: 16,
                     marginHorizontal: "auto",
                  }}
               >
                  Настройки
               </Text>

               <TouchableOpacity style={{ position: "absolute", right: 0 }}>
                  <Text style={{ color: "#00AFCA", fontWeight: 600, fontSize: 13 }}>Сохранить</Text>
               </TouchableOpacity>
            </View>

            <View style={{ width: "90%", marginHorizontal: "auto", marginTop: 30 }}>
               <Text style={{ color: "#242B35", fontWeight: 600, fontSize: 20 }}>Профиль</Text>

               <View style={{ marginTop: 15 }}>
                  <Text>Имя</Text>
                  <TextInput
                     style={{
                        marginTop: 5,
                        backgroundColor: "#F5F5F5",
                        height: 29,
                        borderRadius: 5,
                        borderColor: "#E5E5EA",
                        borderWidth: 1,
                        paddingHorizontal: 5,
                        fontSize: 12,
                        fontWeight: "regular",
                        color: "#242B35",
                     }}
                     placeholder={authState.user?.name}
                     placeholderTextColor={"#8E8E93"}
                  />
               </View>
               <View style={{ marginVertical: 15 }}>
                  <Text>Почта</Text>
                  <TextInput
                     style={{
                        marginTop: 5,
                        backgroundColor: "#F5F5F5",
                        height: 29,
                        borderRadius: 5,
                        borderColor: "#E5E5EA",
                        borderWidth: 1,
                        paddingHorizontal: 5,
                        fontSize: 12,
                        fontWeight: "regular",
                        color: "#242B35",
                     }}
                     placeholder={authState.user?.email}
                     placeholderTextColor={"#8E8E93"}
                  />
               </View>
               <View>
                  <Text>Пароль</Text>
                  <TextInput
                     style={{
                        marginTop: 5,
                        backgroundColor: "#F5F5F5",
                        height: 29,
                        borderRadius: 5,
                        borderColor: "#E5E5EA",
                        borderWidth: 1,
                        paddingHorizontal: 5,
                        fontSize: 12,
                        fontWeight: "regular",
                        color: "#8E8E93",
                     }}
                     value="asdjsadkj"
                     secureTextEntry
                     placeholderTextColor={"#8E8E93"}
                  />
               </View>
            </View>

            <View style={{ width: "90%", marginHorizontal: "auto", marginTop: 30 }}>
               <Text
                  style={{
                     color: "#242B35",
                     fontWeight: 600,
                     fontSize: 20,
                  }}
               >
                  Аккаунт
               </Text>

               {/* <View
                  style={{
                     width: "100%",
                     marginHorizontal: "auto",
                     borderWidth: 1,
                     borderRadius: 5,
                     borderColor: "#E1E1E1",
                     marginVertical: 15,
                  }}
               >
                  <View style={{ padding: 10 }}>
                     <Text
                        style={{
                           fontWeight: 500,
                           fontSize: 14,
                           color: "#242B35",
                           marginBottom: 5,
                        }}
                     >
                        Премиум - Месячный план
                     </Text>
                     <Text
                        style={{
                           fontSize: 12,
                           fontWeight: 500,
                           color: "#8E8E93",
                           lineHeight: 14,
                        }}
                     >
                        Действует до 12 Декабрь 2024 (10 дней осталось)
                     </Text>
                  </View>
               </View> */}
               {/* <TouchableOpacity
                  style={{
                     width: "100%",
                     borderRadius: 12,
                     borderWidth: 1.3,
                     borderColor: "#E1E1E1",
                     marginBottom: 15,
                  }}
               >
                  <Text
                     style={{
                        marginHorizontal: "auto",
                        paddingVertical: 8,
                        color: "#00AFCA",
                        fontWeight: 700,
                        fontSize: 13,
                     }}
                  >
                     Поменять план
                  </Text>
               </TouchableOpacity> */}
               <TouchableOpacity
                  style={{
                     width: "100%",
                     borderRadius: 12,
                     borderWidth: 1.3,
                     borderColor: "#E1E1E1",
                     marginVertical: 15,
                  }}
                  onPress={logout}
               >
                  <Text
                     style={{
                        marginHorizontal: "auto",
                        paddingVertical: 8,
                        color: "#00AFCA",
                        fontWeight: 700,
                        fontSize: 13,
                     }}
                  >
                     Выйти с аккаунта
                  </Text>
               </TouchableOpacity>
               <TouchableOpacity
                  style={{
                     width: "100%",
                     borderRadius: 12,
                     borderWidth: 1.3,
                     borderColor: "#E1E1E1",
                  }}
               >
                  <Text
                     style={{
                        marginHorizontal: "auto",
                        paddingVertical: 8,
                        color: "#00AFCA",
                        fontWeight: 700,
                        fontSize: 13,
                     }}
                  >
                     Сбросить прогресс
                  </Text>
               </TouchableOpacity>
            </View>
         </SafeAreaView>
      </SafeAreaProvider>
   );
}

const styles = StyleSheet.create({});
