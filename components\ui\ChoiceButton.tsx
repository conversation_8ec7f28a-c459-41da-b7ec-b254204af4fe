import React from "react";
import { TouchableOpacity, Text, StyleSheet, View } from "react-native";

interface ButtonProps {
   title: string;
   level: number;
   handlePress: () => void;
   isPicked: boolean;
}

const ChoiceButton: React.FC<ButtonProps> = ({
   title,
   handlePress,
   level,
   isPicked,
}) => {
   return (
      <TouchableOpacity onPress={handlePress} style={styles.buttonContainer}>
         <View
            style={{
               ...styles.button,
               backgroundColor: isPicked ? "#bfebf1" : "#fff",
            }}
         >
            <View style={styles.levelContainer}>
               <LevelDot filled={level >= 1} />
               <LevelDot filled={level >= 2} />
               <LevelDot filled={level >= 3} />
               <Text style={styles.buttonText}>{title}</Text>
            </View>
         </View>
      </TouchableOpacity>
   );
};

interface LevelDotProps {
   filled: boolean;
}

const LevelDot: React.FC<LevelDotProps> = ({ filled }) => {
   return (
      <View
         style={{
            ...styles.dot,
            backgroundColor: filled ? "#00AFCA" : "#D3D3D3",
         }}
      ></View>
   );
};

const styles = StyleSheet.create({
   buttonContainer: {
      marginHorizontal: "auto",
      alignItems: "center",
   },
   button: {
      width: 341,
      height: 54,
      borderWidth: 1.4,
      borderColor: "#00AFCA",
      borderRadius: 22,
      shadowColor: "#000", // Черный цвет тени
      shadowOffset: {
         width: 0,
         height: 4, // Смещение вниз
      },
      shadowOpacity: 0.1, // Прозрачность тени
      shadowRadius: 4, // Радиус размытия тени
      elevation: 4, // Высота тени для Android
      justifyContent: "center",
   },
   levelContainer: {
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      marginLeft: 23,
   },
   dot: {
      width: 10,
      height: 10,
      borderRadius: 10,
      marginRight: 4,
   },
   buttonText: {
      fontWeight: "600",
      fontSize: 16,
      lineHeight: 18,
      color: "#04212F",
      marginLeft: 20,
   },
});

export default ChoiceButton;
