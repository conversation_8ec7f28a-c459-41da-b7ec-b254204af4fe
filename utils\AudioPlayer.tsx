import React, { useEffect, useRef, useImperative<PERSON><PERSON><PERSON>, forwardRef } from "react";
import { Audio } from "expo-av";

interface AudioPlayerProps {
   url: string;
   autoPlay?: boolean;
   onPlay?: () => void;
   onPause?: () => void;
   onStop?: () => void;
   onEnd?: () => void;
   onError?: (error: Error) => void;
}

export interface AudioPlayerRef {
   play: () => Promise<void>;
   pause: () => Promise<void>;
   stop: () => Promise<void>;
}

export const AudioPlayer = forwardRef<AudioPlayerRef, AudioPlayerProps>(
   ({ url, autoPlay = false, onPlay, onPause, onStop, onEnd, onError }, ref) => {
      const soundRef = useRef<Audio.Sound | null>(null);

      useEffect(() => {
         let isMounted = true;

         const loadSound = async () => {
            try {
               const { sound } = await Audio.Sound.createAsync({ uri: url }, { shouldPlay: autoPlay }, (status) => {
                  if (!isMounted) return;
                  if (status.isLoaded && status.didJustFinish) {
                     onEnd?.();
                  }
               });

               soundRef.current = sound;
               if (autoPlay) {
                  onPlay?.();
               }
            } catch (err: any) {
               onError?.(err);
            }
         };

         loadSound();

         return () => {
            isMounted = false;
            soundRef.current?.unloadAsync();
            soundRef.current = null;
         };
      }, [url, autoPlay, onPlay, onEnd, onError]);

      const play = async () => {
         try {
            const status = await soundRef.current?.getStatusAsync();
            // Если трек закончился (позиция равна длительности), сместим на начало
            if (status?.isLoaded && status.durationMillis && status.positionMillis === status.durationMillis) {
               // @ts-ignore
               await soundRef.current.setPositionAsync(0);
            }
            await soundRef.current?.playAsync();
            onPlay?.();
         } catch (err: any) {
            onError?.(err);
         }
      };

      const pause = async () => {
         try {
            await soundRef.current?.pauseAsync();
            onPause?.();
         } catch (err: any) {
            onError?.(err);
         }
      };

      const stop = async () => {
         try {
            await soundRef.current?.stopAsync();
            onStop?.();
         } catch (err: any) {
            onError?.(err);
         }
      };

      useImperativeHandle(ref, () => ({
         play,
         pause,
         stop,
      }));

      return null;
   }
);
