# Push Notifications Integration - Kazakh Lingo Mobile

## Обзор

Интеграция системы Push уведомлений в мобильное приложение Kazakh Lingo завершена. Система использует **Firebase Cloud Messaging (FCM)** для получения нативных FCM токенов, что обеспечивает полную совместимость с backend API.

## Установленные зависимости

```bash
npm install expo-notifications expo-device @react-native-firebase/app @react-native-firebase/messaging --legacy-peer-deps
```

## Структура файлов

### Основные файлы
- `utils/NotificationService.ts` - Основной сервис для работы с уведомлениями
- `utils/useNotifications.ts` - React хуки для управления уведомлениями
- `utils/service.ts` - Обновлен с API endpoints для уведомлений
- `context/AuthContext.tsx` - Интегрирован с системой уведомлений

### Компоненты UI
- `components/NotificationSettings.tsx` - Настройки уведомлений
- `components/NotificationHistory.tsx` - История уведомлений
- `components/NotificationDemo.tsx` - Демо компонент для тестирования

### Конфигурация
- `app.json` - Обновлен с настройками expo-notifications и Firebase
- `android/app/google-services.json` - Конфигурация Firebase для Android
- `ios/GoogleService-Info.plist` - Конфигурация Firebase для iOS

## Основные возможности

### 1. NotificationService
Singleton сервис для управления уведомлениями:
- Инициализация и получение разрешений FCM
- Регистрация FCM Token (нативный Firebase токен)
- Обработка входящих FCM уведомлений
- Поддержка фоновых уведомлений
- Управление badge count
- Очистка уведомлений

### 2. API Integration
Полная интеграция с backend API:
- Регистрация токенов устройств
- Управление настройками уведомлений
- Получение истории уведомлений
- Деактивация токенов
- Отправка тестовых уведомлений

### 3. React Hooks
Удобные хуки для использования в компонентах:
- `useNotifications()` - основной хук для управления уведомлениями
- `useNotificationSettings()` - управление настройками
- `useNotificationHistory()` - работа с историей

### 4. UI Components
Готовые компоненты для интеграции:
- Настройки уведомлений с переключателями
- История уведомлений с пагинацией
- Демо компонент для тестирования

## Использование

### Базовая интеграция

```tsx
import { useNotifications } from '../utils/useNotifications';

const MyComponent = () => {
  const { 
    isInitialized, 
    token, 
    permissionStatus, 
    requestPermissions 
  } = useNotifications();

  // Компонент автоматически инициализируется
  // Используйте состояния для отображения UI
};
```

### Настройки уведомлений

```tsx
import NotificationSettings from '../components/NotificationSettings';

const SettingsScreen = () => {
  return <NotificationSettings />;
};
```

### История уведомлений

```tsx
import NotificationHistory from '../components/NotificationHistory';

const HistoryScreen = () => {
  return <NotificationHistory />;
};
```

## Интеграция с AuthContext

Система автоматически интегрирована с AuthContext:
- При входе пользователя токен устройства регистрируется на сервере
- При выходе токен деактивируется и уведомления очищаются

## Конфигурация app.json

```json
{
  "expo": {
    "plugins": [
      "expo-font",
      [
        "expo-notifications",
        {
          "icon": "./assets/icon.png",
          "color": "#ffffff",
          "defaultChannel": "default"
        }
      ]
    ]
  }
}
```

## API Endpoints

Интегрированы следующие endpoints:
- `POST /v1/notifications/device-token` - Регистрация токена
- `GET /v1/notifications/device-tokens` - Получение токенов
- `DELETE /v1/notifications/device-token` - Деактивация токена
- `GET /v1/notifications/settings` - Получение настроек
- `PUT /v1/notifications/settings` - Обновление настроек
- `GET /v1/notifications/history` - История уведомлений
- `POST /v1/notifications/test` - Тестовое уведомление

## Типы уведомлений

Поддерживаются следующие типы:
- `achievement` - Достижения (🏆)
- `reminder` - Напоминания (⏰)
- `new_content` - Новый контент (📚)
- `progress` - Прогресс (📈)
- `welcome` - Приветствие (👋)
- `streak_lost` - Потеря серии (💔)
- `streak_milestone` - Веха серии (🔥)

## Тестирование

### Локальное тестирование
1. Используйте `NotificationDemo` компонент
2. Запросите разрешения
3. Отправьте тестовое уведомление
4. Проверьте настройки и историю

### Тестирование с сервером
1. Убедитесь, что backend API запущен
2. Войдите в приложение
3. Токен автоматически зарегистрируется
4. Используйте admin панель для отправки уведомлений

## Troubleshooting

### Проблемы с разрешениями
- Убедитесь, что тестируете на физическом устройстве
- Проверьте настройки уведомлений в системе
- Переустановите приложение если разрешения были отклонены

### Проблемы с токеном
- Проверьте projectId в app.json
- Убедитесь, что устройство подключено к интернету
- Проверьте логи на наличие ошибок

### Проблемы с API
- Проверьте подключение к серверу
- Убедитесь, что пользователь авторизован
- Проверьте правильность endpoints

## Следующие шаги

1. Интегрируйте компоненты в основное приложение
2. Настройте дизайн под ваш UI kit
3. Добавьте дополнительные типы уведомлений
4. Настройте автоматические напоминания
5. Добавьте аналитику уведомлений

## Безопасность

- Токены устройств автоматически деактивируются при выходе
- API требует авторизации для всех операций
- Персональные данные не передаются в уведомлениях
- Поддерживается отключение уведомлений пользователем
