import React, { useState } from "react";
import { SafeAreaView, StatusBar, StyleSheet, View } from "react-native";
import ProgressBar from "../../components/ui/ProgressBar";
import ButtonGradient from "../../components/ui/ButtonGradient";
import First from "./First";
import FirstQuestion from "./FirstQuestion";
import QuestionTitle from "../../components/ui/QuestionTitle";
import SecondQuestion from "./SecondQuestion";
import LottieView from "lottie-react-native"; // для анимации
import ChoiceRequiredButton from "../../components/ui/ChoiceRequiredButton";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store/store";
import { Success } from "../../assets/lottie";
import { setValid } from "../../store/slices/buttonSlice";

export default function KnowledgeLevel({ navigation }: { navigation: any }) {
   const [progress, setProgress] = useState(0);
   const [pages, setPages] = useState(0);
   const [isAnimationPlaying, setIsAnimationPlaying] = useState(false);

   const isValid = useSelector((state: RootState) => state.button.isValid);
   const dispatch = useDispatch();

   const handleIncreaseProgress = () => {
      if (pages < 2) {
         setPages(pages + 1);
         setProgress((prev) => Math.min(prev + 34, 100));
         dispatch(setValid(false));
      } else if (pages == 2) {
         setProgress((prev) => Math.min(prev + 34, 100));
         // Запуск анимации на последнем шаге опроса
         setIsAnimationPlaying(true);
         setPages(pages + 1);
         dispatch(setValid(false));
      }
   };

   const handleAnimationFinish = () => {
      // Переход на экран Promo после завершения анимации
      navigation.navigate("Promo");
   };

   const renderContent = () => {
      if (pages === 0) {
         return <First handlePress={handleIncreaseProgress} />;
      }
      if (pages === 1) {
         return <FirstQuestion />;
      }
      if (pages === 2) {
         return <SecondQuestion />;
      }
      if (isAnimationPlaying) {
         return (
            <View
               style={{
                  flex: 1,
                  justifyContent: "center",
                  alignItems: "center",
                  backgroundColor: "#fff",
               }}
            >
               <LottieView
                  source={Success} // Замените на ваш путь к JSON файлу
                  autoPlay
                  loop={false}
                  onAnimationFinish={handleAnimationFinish}
                  style={styles.animation}
               />
            </View>
         );
      }
   };

   const renderTitle = () => {
      if (pages === 0) return null;
      if (pages === 1)
         return <QuestionTitle title="Какой у вас уровень казахского?" />;
      if (pages === 2)
         return <QuestionTitle title="Для чего вы изучаете казахский?" />;
      if (pages === 3) return <QuestionTitle title="Отлично!" />;
   };

   const renderButton = () => {
      if (pages === 0) {
         return (
            <ButtonGradient
               handlePress={handleIncreaseProgress}
               title="Продолжить"
            />
         );
      }
      if (pages === 1) {
         return (
            <ChoiceRequiredButton
               isValid={isValid}
               handlePress={handleIncreaseProgress}
               title="Далее"
            />
         );
      }
      if (pages === 2) {
         return (
            <ChoiceRequiredButton
               isValid={isValid}
               handlePress={handleIncreaseProgress}
               title="Далее"
            />
         );
      }
      if (pages === 3) {
         return (
            <ChoiceRequiredButton
               isValid={isValid}
               handlePress={handleIncreaseProgress}
               title="Далее"
            />
         );
      }
   };

   return (
      <SafeAreaView style={styles.container}>
         <StatusBar />
         <View style={{ marginTop: 24 }}>
            <ProgressBar progress={progress} height={20} />
         </View>
         <View style={styles.content}>
            {renderTitle()}
            {renderContent()}
            {renderButton()}
         </View>
      </SafeAreaView>
   );
}

const styles = StyleSheet.create({
   container: {
      flex: 1,
      backgroundColor: "#fff",
   },
   content: {
      marginTop: 64,
      height: "80%",
      justifyContent: "space-between",
   },
   animation: {
      width: 200,
      height: 200,
      alignSelf: "center",
   },
});
