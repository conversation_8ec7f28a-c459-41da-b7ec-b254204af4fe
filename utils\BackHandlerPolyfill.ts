import { BackHandler } from 'react-native';

// Polyfill for the deprecated removeEventListener method
if (!BackHandler.removeEventListener) {
  // Store subscriptions to be able to remove them later
  const subscriptions = new Map();
  
  // Override addEventListener to store subscriptions
  const originalAddEventListener = BackHandler.addEventListener;
  BackHandler.addEventListener = (eventName: string, handler: () => boolean) => {
    const subscription = originalAddEventListener(eventName, handler);
    subscriptions.set(handler, subscription);
    return subscription;
  };
  
  // Add the missing removeEventListener method
  BackHandler.removeEventListener = (eventName: string, handler: () => boolean) => {
    const subscription = subscriptions.get(handler);
    if (subscription) {
      subscription.remove();
      subscriptions.delete(handler);
    }
  };
}

export default BackHandler;
