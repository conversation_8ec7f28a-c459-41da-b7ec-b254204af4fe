import { View } from "react-native";
import Svg, { Path, Circle, Ellipse } from "react-native-svg";

const ProfileSvg = ({ color }: { color: string }) => (
   <View style={{ backgroundColor: color, borderRadius: 14, padding: 1, margin: "auto" }}>
      <Svg width="40" height="40" viewBox="0 0 49 50" fill="none">
         <Circle cx="24.4999" cy="12.3124" r="8.16667" fill="#00AFCA" />
         <Ellipse opacity="0.5" cx="24.4999" cy="34.7709" rx="14.2917" ry="8.16667" fill="#00AFCA" stroke="#00AFCA" />
      </Svg>
   </View>
);

export default ProfileSvg;
