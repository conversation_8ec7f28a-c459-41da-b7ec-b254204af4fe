import { StyleSheet, Text, TouchableOpacity, View, Animated, Alert, Platform } from "react-native";
import React, { useEffect, useRef } from "react";
import ReportSvg from "../../assets/svg/ReportSvg";
import AudioSvg from "../../assets/svg/AudioSvg";
import ChoiceRequiredButton from "../../components/ui/ChoiceRequiredButton";
import { AudioPlayer, AudioPlayerRef } from "../../utils/AudioPlayer";

export default function LessonAnswerBottom({
   heigth = 300,
   isCorrect,
   onNext,
   question,
}: {
   isCorrect: boolean;
   onNext: () => void;
   question: any;
   heigth?: number;
}) {
   const translateYAnim = useRef(new Animated.Value(220)).current;

   useEffect(() => {
      Animated.timing(translateYAnim, {
         toValue: 0,
         duration: 150,
         useNativeDriver: true,
      }).start();
   }, []);

   const playerRef = useRef<AudioPlayerRef>(null);
   const playSound = async () => {
      playerRef.current?.play();
   };
   return (
      <Animated.View
         style={[
            {
               backgroundColor: isCorrect ? "#cceff4" : "#fbd4ce",
               width: "100%",
               height: heigth,
               position: "absolute",
               borderRadius: 15,
               bottom: 0,
               padding: 20,
               transform: [{ translateY: translateYAnim }],
            },
         ]}
      >
         <View
            style={{
               marginHorizontal: "auto",
               width: "90%",
               display: "flex",
               flexDirection: "row",
               justifyContent: "space-between",
               alignItems: "center",
            }}
         >
            <AudioPlayer
               ref={playerRef}
               url={question.correct_answer.audio_url}
               autoPlay={false}
               // onPlay={() => console.log(question)}
               // onPause={() => console.log("Paused")}
               // onEnd={() => console.log("Ended")}
               // onError={(e) => console.log("Error:", e)}
            />
            <Text
               style={{
                  color: isCorrect ? "#242B35" : "#ED2B08",
                  fontWeight: "700",
                  fontSize: 20,
               }}
            >
               {isCorrect ? "Верно!" : "Правильный ответ"}
            </Text>

            <View
               style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
               }}
            >
               <View style={{ marginRight: 5 }}>
                  <ReportSvg color={isCorrect ? "#00AFCA" : "#ED2B08"} />
               </View>
               <TouchableOpacity onPress={playSound}>
                  <AudioSvg color={isCorrect ? "#00AFCA" : "#ED2B08"} />
               </TouchableOpacity>
            </View>
         </View>

         <View
            style={{
               marginTop: 14,
               marginHorizontal: "auto",
               width: "90%",
               display: "flex",
               flexDirection: "row",
            }}
         >
            <Text
               style={{
                  color: isCorrect ? "#04212F" : "#ED2B08",
                  fontWeight: "500",
                  fontSize: 12,
               }}
            >
               {question.correct_answer?.rus_plaintext} {question.rus_plaintext} -{" "}
            </Text>
            <Text
               style={{
                  color: isCorrect ? "#04212F" : "#ED2B08",
                  fontWeight: "700",
                  fontSize: 12,
               }}
            >
               {question.correct_answer?.plaintext}
               {question.correct_answer?.kaz_plaintext}
               {question.kaz_plaintext}
            </Text>
         </View>

         <View style={{ marginTop: 45 }}>
            <ChoiceRequiredButton
               isValid={true}
               title="Далее"
               color={isCorrect ? "#00AFCA" : "#ED2B08"}
               borderColor={isCorrect ? "#008498" : "#bd2105"}
               handlePress={onNext}
            />
         </View>
      </Animated.View>
   );
}
