import { Text, View } from "react-native";
import React, { useEffect, useState } from "react";
import MessageSvg from "../../../assets/svg/MessageSvg";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import WordList from "./__duolingo_dragdrop/WordList";
import Word from "./__duolingo_dragdrop/Word";
import ChoiceRequiredButton from "../../../components/ui/ChoiceRequiredButton";
import { PlayCorrectSound, PlayWrongSound } from "../../../utils/PlaySound";
import LessonAnswerBottom from "../LessonAnswerBottom";

export default function QuestionContent({
   question,
   onNextQuestion,
   onSaveResult,
}: {
   question: any;
   onNextQuestion: () => void;
   onSaveResult: (isCorrect: boolean) => void;
}) {
   const [words, setWords] = useState(
      question.words?.map((item: any) => ({
         id: item.id,
         word: item.kaz_plaintext,
      }))
   );

   const [result, setResult] = useState([]);

   const [showCorrectAnswer, setShowCorrectAnswer] = useState(false);
   const [showWrongAnswer, setShowWrongAnswer] = useState(false);

   const onReply = () => {
      if (question.correct_answer.sequence.length !== result.length) {
         return;
      }

      const isCorrect = result.every(
         (item, index) =>
            // @ts-ignore
            question.correct_answer.sequence[index] == item.id
      );

      if (!isCorrect) {
         setShowWrongAnswer(true);
         PlayWrongSound();
         onSaveResult(false);
         return;
      }

      setShowCorrectAnswer(true);
      PlayCorrectSound();
      onSaveResult(true);
   };

   const handleNextQuestion = () => {
      setShowCorrectAnswer(false);
      setShowWrongAnswer(false);
      setResult([]);
      onNextQuestion();
   };

   return (
      <View>
         <GestureHandlerRootView
            style={{
               marginTop: 17,
               width: "90%",
               marginHorizontal: "auto",
               height: "100%",
               display: "flex",
               flexDirection: "column",
               justifyContent: "space-between",
            }}
         >
            <View
               style={{
                  backgroundColor: "#cceff4",
                  borderRadius: 22,
                  padding: 15,
                  marginTop: 15,
               }}
            >
               <Text
                  style={{
                     color: "#04212F",
                     fontWeight: 700,
                     fontSize: 16,
                  }}
               >
                  Теория
               </Text>
               <Text
                  style={{
                     fontWeight: 500,
                     fontSize: 12,
                     color: "#242B35",
                     width: "100%",
                     marginHorizontal: "auto",
                     marginTop: 10,
                  }}
               >
                  В казахском языке порядок слов обычно следующий:
               </Text>

               <View
                  style={{
                     display: "flex",
                     flexDirection: "row",
                     justifyContent: "space-between",
                     width: "100%",
                     marginHorizontal: "auto",
                     alignItems: "center",
                  }}
               >
                  <View
                     style={{
                        borderRadius: 5,
                        backgroundColor: "#F7AD3A",
                        marginVertical: 20,
                     }}
                  >
                     <Text
                        style={{
                           padding: 5,
                           fontWeight: 500,
                           fontSize: 12,
                           color: "#fff",
                        }}
                     >
                        Подлежащее
                     </Text>
                  </View>
                  <Text
                     style={{
                        color: "#000000",
                        fontWeight: 500,
                        fontSize: 12,
                     }}
                  >
                     +
                  </Text>
                  <View style={{ borderRadius: 5, backgroundColor: "#34C759" }}>
                     <Text
                        style={{
                           padding: 5,
                           fontWeight: 500,
                           fontSize: 12,
                           color: "#fff",
                        }}
                     >
                        Дополнение
                     </Text>
                  </View>
                  <Text
                     style={{
                        color: "#000000",
                        fontWeight: 500,
                        fontSize: 12,
                     }}
                  >
                     +
                  </Text>
                  <View style={{ borderRadius: 5, backgroundColor: "#007AFF" }}>
                     <Text
                        style={{
                           padding: 5,
                           fontWeight: 500,
                           fontSize: 12,
                           color: "#fff",
                        }}
                     >
                        Сказуемое
                     </Text>
                  </View>
               </View>
            </View>

            <View style={{ marginLeft: 22, marginTop: 35 }}>
               <Text
                  style={{
                     color: "#242B35",
                     fontWeight: 700,
                     fontSize: 20,
                  }}
               >
                  Соберите фразу
               </Text>

               <View
                  style={{
                     display: "flex",
                     flexDirection: "row",
                     alignItems: "center",
                     marginTop: 26,
                  }}
               >
                  <MessageSvg />
                  <View
                     style={{
                        marginLeft: 7,
                     }}
                  >
                     <Text>{question.correct_answer.rus_plaintext} </Text>
                     <View
                        style={{
                           height: 1,
                           marginTop: 3,
                           width: "100%",
                           borderRadius: 1,
                           borderWidth: 1,
                           borderColor: "#8F8F91",
                           borderStyle: "dashed",
                        }}
                     />
                  </View>
               </View>
            </View>
            <View style={{ flex: 1 }}>
               <WordList key={question.id} words={words} setRes={setResult}>
                  {words.map((word: any) => (
                     <Word key={`${word.id}-${question.id}`} {...word} />
                  ))}
               </WordList>
            </View>

            <View style={{ flex: 1, marginTop: 0 }}>
               <ChoiceRequiredButton
                  isValid={question.correct_answer.sequence.length == result.length}
                  handlePress={onReply}
                  title="Проверить"
               />
            </View>
         </GestureHandlerRootView>
         <View style={{}}>
            {showCorrectAnswer && (
               <LessonAnswerBottom heigth={400} onNext={handleNextQuestion} question={question} isCorrect={true} />
            )}
            {showWrongAnswer && (
               <LessonAnswerBottom heigth={400} onNext={handleNextQuestion} question={question} isCorrect={false} />
            )}
         </View>
      </View>
   );
}
