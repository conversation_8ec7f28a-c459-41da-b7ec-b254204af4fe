<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Демо - Система Достижений | Kazakh Lingo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .demo-section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 1.5rem;
            color: #4a5568;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title .icon {
            font-size: 1.8rem;
        }

        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .btn.danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }

        .input-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
            min-width: 200px;
        }

        .input-group label {
            font-weight: 600;
            color: #4a5568;
            font-size: 12px;
            text-transform: uppercase;
        }

        .input-group input, .input-group select {
            padding: 10px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .achievements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .achievement-card {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 12px;
            padding: 20px;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .achievement-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .achievement-card.completed {
            border-left-color: #48bb78;
            background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
        }

        .achievement-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
        }

        .achievement-icon {
            font-size: 2rem;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 50%;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .achievement-info h3 {
            font-size: 1.1rem;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .achievement-info .category {
            font-size: 0.8rem;
            color: #718096;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .achievement-description {
            color: #4a5568;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .progress-bar {
            background: #e2e8f0;
            border-radius: 10px;
            height: 8px;
            margin-bottom: 10px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            height: 100%;
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .progress-fill.completed {
            background: linear-gradient(90deg, #48bb78 0%, #38a169 100%);
        }

        .progress-text {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: #718096;
            font-weight: 600;
        }

        .reward-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            color: #744210;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 700;
            box-shadow: 0 2px 5px rgba(255, 215, 0, 0.3);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            transform: translateX(400px);
            transition: transform 0.3s ease;
            z-index: 1000;
            max-width: 350px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification h4 {
            margin-bottom: 5px;
            font-size: 1.1rem;
        }

        .notification p {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #718096;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8rem;
        }

        .api-demo {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }

        .api-demo pre {
            margin: 0;
            white-space: pre-wrap;
        }

        .json-key {
            color: #63b3ed;
        }

        .json-string {
            color: #68d391;
        }

        .json-number {
            color: #fbb6ce;
        }

        .json-boolean {
            color: #f6ad55;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                flex-direction: column;
            }
            
            .achievements-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏆 Система Достижений</h1>
            <p>Интерактивная демонстрация возможностей Kazakh Lingo</p>
        </div>

        <!-- Статистика -->
        <div class="demo-section">
            <div class="section-title">
                <span class="icon">📊</span>
                Статистика системы
            </div>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalAchievements">42</div>
                    <div class="stat-label">Всего достижений</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="userProgress">0</div>
                    <div class="stat-label">Получено</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalRewards">0</div>
                    <div class="stat-label">Очков заработано</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="completionRate">0%</div>
                    <div class="stat-label">Процент завершения</div>
                </div>
            </div>
        </div>

        <!-- Симуляция действий пользователя -->
        <div class="demo-section">
            <div class="section-title">
                <span class="icon">🎮</span>
                Симуляция действий пользователя
            </div>
            <div class="controls">
                <button class="btn" onclick="simulateAction('lesson')">
                    📚 Завершить урок
                </button>
                <button class="btn" onclick="simulateAction('words')">
                    📝 Изучить 3 слова
                </button>
                <button class="btn" onclick="simulateAction('questions')">
                    ❓ Ответить на 5 вопросов
                </button>
                <button class="btn" onclick="simulateAction('module')">
                    📖 Завершить модуль
                </button>
                <button class="btn secondary" onclick="simulateAction('streak')">
                    🔥 Обновить стрик (+1 день)
                </button>
                <button class="btn danger" onclick="resetProgress()">
                    🔄 Сбросить прогресс
                </button>
            </div>
        </div>

        <!-- Создание достижения (Админ панель) -->
        <div class="demo-section">
            <div class="section-title">
                <span class="icon">⚙️</span>
                Админ панель - Создание достижения
            </div>
            <div class="controls">
                <div class="input-group">
                    <label>Название</label>
                    <input type="text" id="achievementName" placeholder="Название достижения">
                </div>
                <div class="input-group">
                    <label>Описание</label>
                    <input type="text" id="achievementDesc" placeholder="Описание">
                </div>
                <div class="input-group">
                    <label>Тип</label>
                    <select id="achievementType">
                        <option value="lessons">Уроки</option>
                        <option value="words">Слова</option>
                        <option value="streak">Стрик</option>
                        <option value="modules">Модули</option>
                        <option value="questions">Вопросы</option>
                        <option value="consecutive">Подряд правильных</option>
                    </select>
                </div>
                <div class="input-group">
                    <label>Цель</label>
                    <input type="number" id="achievementTarget" placeholder="10" min="1">
                </div>
                <div class="input-group">
                    <label>Иконка</label>
                    <input type="text" id="achievementIcon" placeholder="🎯" maxlength="2">
                </div>
                <div class="input-group">
                    <label>Награда</label>
                    <input type="number" id="achievementReward" placeholder="50" min="0">
                </div>
                <div class="input-group">
                    <label>Категория</label>
                    <select id="achievementCategory">
                        <option value="progress">Прогресс</option>
                        <option value="vocabulary">Словарь</option>
                        <option value="consistency">Постоянство</option>
                        <option value="accuracy">Точность</option>
                        <option value="speed">Скорость</option>
                        <option value="general">Общие</option>
                    </select>
                </div>
                <div class="input-group">
                    <label>Сложность</label>
                    <select id="achievementDifficulty">
                        <option value="easy">Легкая</option>
                        <option value="medium">Средняя</option>
                        <option value="hard">Сложная</option>
                    </select>
                </div>
                <button class="btn secondary" onclick="createCustomAchievement()">
                    ➕ Создать достижение
                </button>
            </div>
        </div>

        <!-- Фильтры достижений -->
        <div class="demo-section">
            <div class="section-title">
                <span class="icon">🔍</span>
                Фильтры и поиск
            </div>
            <div class="controls">
                <div class="input-group">
                    <label>Поиск</label>
                    <input type="text" id="searchInput" placeholder="Поиск по названию..." onkeyup="filterAchievements()">
                </div>
                <div class="input-group">
                    <label>Категория</label>
                    <select id="categoryFilter" onchange="filterAchievements()">
                        <option value="">Все категории</option>
                        <option value="progress">Прогресс</option>
                        <option value="vocabulary">Словарь</option>
                        <option value="consistency">Постоянство</option>
                        <option value="accuracy">Точность</option>
                        <option value="speed">Скорость</option>
                        <option value="general">Общие</option>
                    </select>
                </div>
                <div class="input-group">
                    <label>Статус</label>
                    <select id="statusFilter" onchange="filterAchievements()">
                        <option value="">Все</option>
                        <option value="completed">Завершенные</option>
                        <option value="in-progress">В процессе</option>
                        <option value="not-started">Не начатые</option>
                    </select>
                </div>
                <div class="input-group">
                    <label>Сложность</label>
                    <select id="difficultyFilter" onchange="filterAchievements()">
                        <option value="">Все</option>
                        <option value="easy">Легкие</option>
                        <option value="medium">Средние</option>
                        <option value="hard">Сложные</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Список достижений -->
        <div class="demo-section">
            <div class="section-title">
                <span class="icon">🏆</span>
                Достижения пользователя
            </div>
            <div class="achievements-grid" id="achievementsGrid">
                <!-- Достижения будут добавлены через JavaScript -->
            </div>
        </div>

        <!-- API Demo -->
        <div class="demo-section">
            <div class="section-title">
                <span class="icon">🔧</span>
                API Демонстрация
            </div>
            <div class="controls">
                <button class="btn" onclick="showApiDemo('user-achievements')">
                    👤 GET /v1/achievements/:id
                </button>
                <button class="btn" onclick="showApiDemo('all-achievements')">
                    📋 GET /v1/achievements-all
                </button>
                <button class="btn" onclick="showApiDemo('create-achievement')">
                    ➕ POST /v1/admin/achievements/create
                </button>
                <button class="btn" onclick="showApiDemo('update-progress')">
                    📈 PATCH /v1/achievements
                </button>
            </div>
            <div class="api-demo" id="apiDemo">
                <pre>Выберите API эндпоинт для демонстрации...</pre>
            </div>
        </div>
    </div>

    <!-- Уведомление -->
    <div class="notification" id="notification">
        <h4 id="notificationTitle">🏆 Новое достижение!</h4>
        <p id="notificationText">Поздравляем с получением достижения!</p>
    </div>

    <script>
        // Данные достижений
        let achievements = [
            // Достижения за уроки
            { id: 1, name: "Первые шаги", description: "Пройдите первый урок", type: "lessons", target: 1, icon: "🎯", reward: 10, category: "progress", difficulty: "easy", progress: 0 },
            { id: 2, name: "Ученик", description: "Пройдите 3 урока", type: "lessons", target: 3, icon: "📚", reward: 25, category: "progress", difficulty: "easy", progress: 0 },
            { id: 3, name: "Студент", description: "Пройдите 7 уроков", type: "lessons", target: 7, icon: "🎓", reward: 50, category: "progress", difficulty: "medium", progress: 0 },
            { id: 4, name: "Знаток", description: "Пройдите 15 уроков", type: "lessons", target: 15, icon: "🏆", reward: 100, category: "progress", difficulty: "medium", progress: 0 },
            { id: 5, name: "Мастер обучения", description: "Пройдите 30 уроков", type: "lessons", target: 30, icon: "👑", reward: 200, category: "progress", difficulty: "hard", progress: 0 },

            // Достижения за слова
            { id: 6, name: "Словарный запас", description: "Выучите 5 новых слов", type: "words", target: 5, icon: "📝", reward: 15, category: "vocabulary", difficulty: "easy", progress: 0 },
            { id: 7, name: "Полиглот", description: "Выучите 10 новых слов", type: "words", target: 10, icon: "🗣️", reward: 30, category: "vocabulary", difficulty: "easy", progress: 0 },
            { id: 8, name: "Лингвист", description: "Выучите 25 новых слов", type: "words", target: 25, icon: "📖", reward: 75, category: "vocabulary", difficulty: "medium", progress: 0 },
            { id: 9, name: "Мастер слов", description: "Выучите 50 новых слов", type: "words", target: 50, icon: "🎭", reward: 150, category: "vocabulary", difficulty: "medium", progress: 0 },

            // Достижения за стрики
            { id: 10, name: "Постоянство", description: "Занимайтесь 3 дня подряд", type: "streak", target: 3, icon: "🔥", reward: 20, category: "consistency", difficulty: "easy", progress: 0 },
            { id: 11, name: "Дисциплина", description: "Занимайтесь 7 дней подряд", type: "streak", target: 7, icon: "💪", reward: 50, category: "consistency", difficulty: "medium", progress: 0 },
            { id: 12, name: "Железная воля", description: "Занимайтесь 30 дней подряд", type: "streak", target: 30, icon: "🛡️", reward: 250, category: "consistency", difficulty: "hard", progress: 0 },

            // Достижения за модули
            { id: 13, name: "Первый модуль", description: "Завершите первый модуль", type: "modules", target: 1, icon: "🎪", reward: 25, category: "progress", difficulty: "easy", progress: 0 },
            { id: 14, name: "Прогресс", description: "Завершите 3 модуля", type: "modules", target: 3, icon: "📈", reward: 60, category: "progress", difficulty: "easy", progress: 0 },
            { id: 15, name: "Целеустремленный", description: "Завершите 5 модулей", type: "modules", target: 5, icon: "🎯", reward: 100, category: "progress", difficulty: "medium", progress: 0 },

            // Достижения за вопросы
            { id: 16, name: "Любознательный", description: "Ответьте на 10 вопросов", type: "questions", target: 10, icon: "❓", reward: 10, category: "progress", difficulty: "easy", progress: 0 },
            { id: 17, name: "Активный ученик", description: "Ответьте на 50 вопросов", type: "questions", target: 50, icon: "✋", reward: 40, category: "progress", difficulty: "easy", progress: 0 },
            { id: 18, name: "Знаток ответов", description: "Ответьте на 100 вопросов", type: "questions", target: 100, icon: "🤔", reward: 80, category: "progress", difficulty: "medium", progress: 0 },

            // Достижения за точность
            { id: 19, name: "Меткий стрелок", description: "Ответьте правильно на 5 вопросов подряд", type: "consecutive", target: 5, icon: "🎯", reward: 30, category: "accuracy", difficulty: "easy", progress: 0 },
            { id: 20, name: "Снайпер", description: "Ответьте правильно на 10 вопросов подряд", type: "consecutive", target: 10, icon: "🏹", reward: 60, category: "accuracy", difficulty: "medium", progress: 0 },

            // Специальные достижения
            { id: 21, name: "Скоростной ученик", description: "Завершите урок менее чем за 2 минуты", type: "speed", target: 1, icon: "⚡", reward: 50, category: "speed", difficulty: "medium", progress: 0 },
            { id: 22, name: "Ранняя пташка", description: "Занимайтесь до 8 утра", type: "general", target: 1, icon: "🌅", reward: 25, category: "general", difficulty: "easy", progress: 0 }
        ];

        let userStats = {
            totalRewards: 0,
            completedAchievements: 0,
            streakDays: 0
        };

        // Инициализация
        document.addEventListener('DOMContentLoaded', function() {
            renderAchievements();
            updateStats();
        });

        // Симуляция действий пользователя
        function simulateAction(actionType) {
            let updatedAchievements = [];

            switch(actionType) {
                case 'lesson':
                    updatedAchievements = updateProgress('lessons', 1);
                    break;
                case 'words':
                    updatedAchievements = updateProgress('words', 3);
                    break;
                case 'questions':
                    updatedAchievements = updateProgress('questions', 5);
                    break;
                case 'module':
                    updatedAchievements = updateProgress('modules', 1);
                    break;
                case 'streak':
                    userStats.streakDays++;
                    updatedAchievements = updateProgress('streak', userStats.streakDays);
                    break;
            }

            // Показываем уведомления о новых достижениях
            updatedAchievements.forEach(achievement => {
                if (achievement.justCompleted) {
                    showNotification(achievement);
                }
            });

            renderAchievements();
            updateStats();
        }

        // Обновление прогресса достижений
        function updateProgress(type, increment) {
            let updatedAchievements = [];

            achievements.forEach(achievement => {
                if (achievement.type === type && achievement.progress < achievement.target) {
                    let oldProgress = achievement.progress;

                    if (type === 'streak') {
                        achievement.progress = increment; // Для стрика устанавливаем абсолютное значение
                    } else {
                        achievement.progress = Math.min(achievement.progress + increment, achievement.target);
                    }

                    // Проверяем, было ли достижение только что завершено
                    if (oldProgress < achievement.target && achievement.progress >= achievement.target) {
                        achievement.justCompleted = true;
                        userStats.totalRewards += achievement.reward;
                        userStats.completedAchievements++;
                        updatedAchievements.push(achievement);
                    }
                }
            });

            return updatedAchievements;
        }

        // Создание пользовательского достижения
        function createCustomAchievement() {
            const name = document.getElementById('achievementName').value;
            const description = document.getElementById('achievementDesc').value;
            const type = document.getElementById('achievementType').value;
            const target = parseInt(document.getElementById('achievementTarget').value);
            const icon = document.getElementById('achievementIcon').value || '🏆';
            const reward = parseInt(document.getElementById('achievementReward').value) || 0;
            const category = document.getElementById('achievementCategory').value;
            const difficulty = document.getElementById('achievementDifficulty').value;

            if (!name || !description || !target) {
                alert('Пожалуйста, заполните все обязательные поля');
                return;
            }

            const newAchievement = {
                id: achievements.length + 1,
                name,
                description,
                type,
                target,
                icon,
                reward,
                category,
                difficulty,
                progress: 0
            };

            achievements.push(newAchievement);

            // Очищаем форму
            document.getElementById('achievementName').value = '';
            document.getElementById('achievementDesc').value = '';
            document.getElementById('achievementTarget').value = '';
            document.getElementById('achievementIcon').value = '';
            document.getElementById('achievementReward').value = '';

            renderAchievements();
            updateStats();

            showNotification({
                name: 'Достижение создано!',
                description: `"${name}" добавлено в систему`,
                icon: '✅'
            });
        }

        // Отображение достижений
        function renderAchievements() {
            const grid = document.getElementById('achievementsGrid');
            const filteredAchievements = getFilteredAchievements();

            grid.innerHTML = filteredAchievements.map(achievement => {
                const progressPercent = Math.min((achievement.progress / achievement.target) * 100, 100);
                const isCompleted = achievement.progress >= achievement.target;
                const difficultyColors = {
                    easy: '#48bb78',
                    medium: '#ed8936',
                    hard: '#f56565'
                };

                return `
                    <div class="achievement-card ${isCompleted ? 'completed' : ''}">
                        <div class="reward-badge">${achievement.reward} очков</div>
                        <div class="achievement-header">
                            <div class="achievement-icon">${achievement.icon}</div>
                            <div class="achievement-info">
                                <h3>${achievement.name}</h3>
                                <div class="category" style="color: ${difficultyColors[achievement.difficulty]}">${getCategoryName(achievement.category)} • ${getDifficultyName(achievement.difficulty)}</div>
                            </div>
                        </div>
                        <div class="achievement-description">${achievement.description}</div>
                        <div class="progress-bar">
                            <div class="progress-fill ${isCompleted ? 'completed' : ''}" style="width: ${progressPercent}%"></div>
                        </div>
                        <div class="progress-text">
                            <span>${achievement.progress} / ${achievement.target}</span>
                            <span>${Math.round(progressPercent)}%</span>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Фильтрация достижений
        function getFilteredAchievements() {
            const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
            const categoryFilter = document.getElementById('categoryFilter')?.value || '';
            const statusFilter = document.getElementById('statusFilter')?.value || '';
            const difficultyFilter = document.getElementById('difficultyFilter')?.value || '';

            return achievements.filter(achievement => {
                const matchesSearch = achievement.name.toLowerCase().includes(searchTerm) ||
                                    achievement.description.toLowerCase().includes(searchTerm);
                const matchesCategory = !categoryFilter || achievement.category === categoryFilter;
                const matchesDifficulty = !difficultyFilter || achievement.difficulty === difficultyFilter;

                let matchesStatus = true;
                if (statusFilter === 'completed') {
                    matchesStatus = achievement.progress >= achievement.target;
                } else if (statusFilter === 'in-progress') {
                    matchesStatus = achievement.progress > 0 && achievement.progress < achievement.target;
                } else if (statusFilter === 'not-started') {
                    matchesStatus = achievement.progress === 0;
                }

                return matchesSearch && matchesCategory && matchesDifficulty && matchesStatus;
            });
        }

        function filterAchievements() {
            renderAchievements();
        }

        // Обновление статистики
        function updateStats() {
            const completedCount = achievements.filter(a => a.progress >= a.target).length;
            const totalRewards = achievements
                .filter(a => a.progress >= a.target)
                .reduce((sum, a) => sum + a.reward, 0);
            const completionRate = Math.round((completedCount / achievements.length) * 100);

            document.getElementById('totalAchievements').textContent = achievements.length;
            document.getElementById('userProgress').textContent = completedCount;
            document.getElementById('totalRewards').textContent = totalRewards;
            document.getElementById('completionRate').textContent = completionRate + '%';
        }

        // Показ уведомления
        function showNotification(achievement) {
            const notification = document.getElementById('notification');
            const title = document.getElementById('notificationTitle');
            const text = document.getElementById('notificationText');

            title.textContent = `${achievement.icon} Новое достижение!`;
            text.textContent = `Поздравляем! Вы получили достижение "${achievement.name}"`;

            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 4000);
        }

        // Сброс прогресса
        function resetProgress() {
            if (confirm('Вы уверены, что хотите сбросить весь прогресс?')) {
                achievements.forEach(achievement => {
                    achievement.progress = 0;
                    achievement.justCompleted = false;
                });
                userStats = {
                    totalRewards: 0,
                    completedAchievements: 0,
                    streakDays: 0
                };
                renderAchievements();
                updateStats();

                showNotification({
                    name: 'Прогресс сброшен',
                    description: 'Все достижения сброшены',
                    icon: '🔄'
                });
            }
        }

        // API демонстрация
        function showApiDemo(type) {
            const apiDemo = document.getElementById('apiDemo');
            let content = '';

            switch(type) {
                case 'user-achievements':
                    content = `<span class="json-key">GET</span> /v1/achievements/123

<span class="json-key">Response:</span>
{
  <span class="json-key">"user_achievements"</span>: [
    {
      <span class="json-key">"id"</span>: <span class="json-number">1</span>,
      <span class="json-key">"achievement_id"</span>: <span class="json-number">1</span>,
      <span class="json-key">"user_id"</span>: <span class="json-number">123</span>,
      <span class="json-key">"progress"</span>: <span class="json-number">1</span>,
      <span class="json-key">"achieved"</span>: <span class="json-boolean">true</span>,
      <span class="json-key">"achieved_at"</span>: <span class="json-string">"2025-08-05T10:30:00Z"</span>,
      <span class="json-key">"reward_claimed"</span>: <span class="json-boolean">true</span>,
      <span class="json-key">"achievement"</span>: {
        <span class="json-key">"id"</span>: <span class="json-number">1</span>,
        <span class="json-key">"name"</span>: <span class="json-string">"Первые шаги"</span>,
        <span class="json-key">"description"</span>: <span class="json-string">"Пройдите первый урок"</span>,
        <span class="json-key">"type"</span>: <span class="json-string">"lessons"</span>,
        <span class="json-key">"target"</span>: <span class="json-number">1</span>,
        <span class="json-key">"icon"</span>: <span class="json-string">"🎯"</span>,
        <span class="json-key">"reward"</span>: <span class="json-number">10</span>,
        <span class="json-key">"category"</span>: <span class="json-string">"progress"</span>,
        <span class="json-key">"difficulty"</span>: <span class="json-string">"easy"</span>
      }
    }
  ]
}`;
                    break;

                case 'all-achievements':
                    content = `<span class="json-key">GET</span> /v1/achievements-all

<span class="json-key">Response:</span>
{
  <span class="json-key">"achievements"</span>: [
    {
      <span class="json-key">"id"</span>: <span class="json-number">1</span>,
      <span class="json-key">"name"</span>: <span class="json-string">"Первые шаги"</span>,
      <span class="json-key">"description"</span>: <span class="json-string">"Пройдите первый урок"</span>,
      <span class="json-key">"type"</span>: <span class="json-string">"lessons"</span>,
      <span class="json-key">"target"</span>: <span class="json-number">1</span>,
      <span class="json-key">"icon"</span>: <span class="json-string">"🎯"</span>,
      <span class="json-key">"reward"</span>: <span class="json-number">10</span>,
      <span class="json-key">"category"</span>: <span class="json-string">"progress"</span>,
      <span class="json-key">"difficulty"</span>: <span class="json-string">"easy"</span>
    }
  ]
}`;
                    break;

                case 'create-achievement':
                    content = `<span class="json-key">POST</span> /v1/admin/achievements/create
<span class="json-key">Authorization:</span> Bearer admin_token

<span class="json-key">Request Body:</span>
{
  <span class="json-key">"name"</span>: <span class="json-string">"Мастер казахского"</span>,
  <span class="json-key">"description"</span>: <span class="json-string">"Выучите 100 слов"</span>,
  <span class="json-key">"type"</span>: <span class="json-string">"words"</span>,
  <span class="json-key">"target"</span>: <span class="json-number">100</span>,
  <span class="json-key">"icon"</span>: <span class="json-string">"🏆"</span>,
  <span class="json-key">"reward"</span>: <span class="json-number">500</span>,
  <span class="json-key">"category"</span>: <span class="json-string">"vocabulary"</span>,
  <span class="json-key">"difficulty"</span>: <span class="json-string">"hard"</span>
}

<span class="json-key">Response:</span>
{
  <span class="json-key">"achievement"</span>: {
    <span class="json-key">"id"</span>: <span class="json-number">23</span>,
    <span class="json-key">"name"</span>: <span class="json-string">"Мастер казахского"</span>,
    <span class="json-key">"created_at"</span>: <span class="json-string">"2025-08-05T12:00:00Z"</span>
  }
}`;
                    break;

                case 'update-progress':
                    content = `<span class="json-key">PATCH</span> /v1/achievements
<span class="json-key">Authorization:</span> Bearer user_token

<span class="json-key">Request Body:</span>
{
  <span class="json-key">"user_id"</span>: <span class="json-number">123</span>,
  <span class="json-key">"achievement_id"</span>: <span class="json-number">2</span>,
  <span class="json-key">"progress"</span>: <span class="json-number">1</span>
}

<span class="json-key">Response:</span>
{
  <span class="json-key">"result"</span>: {
    <span class="json-key">"achievement_name"</span>: <span class="json-string">"Словарный запас"</span>,
    <span class="json-key">"newly_achieved"</span>: <span class="json-boolean">false</span>,
    <span class="json-key">"previous_progress"</span>: <span class="json-number">3</span>,
    <span class="json-key">"current_progress"</span>: <span class="json-number">4</span>,
    <span class="json-key">"target"</span>: <span class="json-number">5</span>,
    <span class="json-key">"reward"</span>: <span class="json-number">15</span>
  }
}`;
                    break;
            }

            apiDemo.innerHTML = `<pre>${content}</pre>`;
        }

        // Вспомогательные функции
        function getCategoryName(category) {
            const names = {
                progress: 'Прогресс',
                vocabulary: 'Словарь',
                consistency: 'Постоянство',
                accuracy: 'Точность',
                speed: 'Скорость',
                general: 'Общие'
            };
            return names[category] || category;
        }

        function getDifficultyName(difficulty) {
            const names = {
                easy: 'Легкая',
                medium: 'Средняя',
                hard: 'Сложная'
            };
            return names[difficulty] || difficulty;
        }
    </script>
</body>
</html>
