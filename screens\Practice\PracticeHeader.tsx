import {
   Image,
   Platform,
   SafeAreaView,
   StyleSheet,
   Text,
   View,
} from "react-native";
import React from "react";
import { LinearGradient } from "expo-linear-gradient";
import { Girl } from "../../assets/image";
import PromoButton from "../../components/ui/PromoButton";
import PracticeHeaderButton from "./PracticeHeaderButton";

export default function PracticeHeader() {
   return (
      <LinearGradient
         colors={["#9845E8", "#3D68DE", "#33D2FF"]} // Цвета градиента
         start={{ x: 0, y: 0 }}
         end={{ x: 1, y: 1 }} // Направление градиента
         style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            maxHeight: 220,
         }}
      >
         <SafeAreaView
            style={{
               width: "100%",
               marginTop: Platform.OS === "android" ? 30 : 0,
            }}
         >
            <View
               style={{
                  width: "90%",
                  height: 139,
                  marginHorizontal: "auto",
                  backgroundColor: "rgba(255, 255, 255, 0.25)",
                  borderRadius: 5,
                  position: "relative", // Устанавливаем позиционирование относительно контейнера
               }}
            >
               <View
                  style={{
                     display: "flex",
                     flexDirection: "row",
                     justifyContent: "space-between",
                     marginLeft: 20,
                  }}
               >
                  <View style={{ marginVertical: 17 }}>
                     <Text
                        style={{
                           fontWeight: 700,
                           fontSize: 20,
                           color: "#fff",
                           lineHeight: 24,
                        }}
                     >
                        Отточите навыки
                     </Text>
                     <Text
                        style={{
                           fontWeight: 400,
                           fontSize: 12,
                           color: "#fff",
                           lineHeight: 14,
                           width: 197,
                           marginTop: 5,
                        }}
                     >
                        Повторите изученные слова с фокусом на аудирование
                     </Text>

                     <View
                        style={{
                           display: "flex",
                           flexDirection: "row",
                           alignItems: "center",
                           marginTop: 20,
                        }}
                     >
                        <PracticeHeaderButton
                           title="Начать"
                           handlePress={() => {}}
                        />
                        <Text
                           style={{
                              fontWeight: 700,
                              fontSize: 14,
                              color: "#fff",
                              marginLeft: 10,
                           }}
                        >
                           +20 XP
                        </Text>
                     </View>
                  </View>
               </View>

               {/* Абсолютное позиционирование для изображения девочки */}
               <Image
                  source={Girl}
                  style={{
                     position: "absolute",
                     right: 0,
                     bottom: 0,
                     resizeMode: "contain",
                  }}
               />
            </View>
         </SafeAreaView>
      </LinearGradient>
   );
}
