import React, { useState } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import MessageSvg from "../../../assets/svg/MessageSvg";
import ChoiceRequiredButton from "../../../components/ui/ChoiceRequiredButton";
import LessonAnswerBottom from "../LessonAnswerBottom";
import { PlayCorrectSound, PlayWrongSound } from "../../../utils/PlaySound";
import { Question } from "../../../types/module";

const keyLayout = [
   ["ә", "і", "ң", "ғ", "ұ", "ү", "қ", "ө", "һ"],
   ["й", "ц", "у", "к", "е", "н", "г", "ш", "щ", "з", "х", "ъ"],
   ["ф", "ы", "в", "а", "п", "р", "о", "л", "д", "ж", "э"],
   ["я", "ч", "с", "м", "и", "т", "ь", "б", "ю", "[<-]"],
];

// @ts-ignore
export default function QuestionContent({ onNextQuestion, onSaveResult, question }: { question: Question }) {
   const [inputText, setInputText] = useState("");

   const expectedText = question.words[0].kaz_plaintext;

   // Подготовка массива символов с учетом пробелов
   const expectedChars = expectedText.split("").map((char: any) => ({
      char,
      isSpace: char === " ",
   }));
   const maxInputLength = expectedChars.filter((item: any) => !item.isSpace).length;

   const handleKeyPress = (key: string) => {
      if (key === "[<-]") {
         setInputText((prevText) => prevText.slice(0, -1));
      } else {
         setInputText((prevText) => {
            if (prevText.length < maxInputLength) {
               return prevText + key;
            } else {
               return prevText;
            }
         });
      }
   };

   const [showCorrectAnswer, setShowCorrectAnswer] = useState(false);
   const [showWrongAnswer, setShowWrongAnswer] = useState(false);

   const handlePress = () => {
      setInputText("");
      setShowCorrectAnswer(false);
      setShowWrongAnswer(false);

      onNextQuestion();
   };

   const handleAnswer = () => {
      const expectedInput = expectedText.replace(/ /g, "");
      if (inputText.toLowerCase() === expectedInput.toLowerCase()) {
         PlayCorrectSound();
         onSaveResult(true);
         setShowCorrectAnswer(true);
      } else {
         PlayWrongSound();
         onSaveResult(false);
         setShowWrongAnswer(true);
      }
   };

   return (
      <View style={{ height: "100%" }}>
         <View style={styles.container}>
            <View style={styles.content}>
               {/* Основной контент */}
               <View style={{ paddingHorizontal: 45 }}>
                  <Text style={{ color: "#04212F", fontWeight: "700", fontSize: 20 }}>Введите перевод</Text>

                  <View
                     style={{
                        flexDirection: "row",
                        alignItems: "center",
                        marginTop: 26,
                     }}
                  >
                     <MessageSvg />
                     <View style={{ marginLeft: 7 }}>
                        <Text>{question.words[0].rus_plaintext}</Text>
                        <View
                           style={{
                              height: 1,
                              marginTop: 3,
                              width: "100%",
                              borderRadius: 1,
                              backgroundColor: "#ccc",
                           }}
                        />
                     </View>
                  </View>
               </View>

               {/* Плейсхолдеры перед клавиатурой */}
               <View style={styles.placeholderContainer}>
                  <View style={{ flexDirection: "row", justifyContent: "center" }}>
                     {(() => {
                        let inputIndex = 0;
                        // @ts-ignore
                        return expectedChars.map((item, index) => {
                           if (item.isSpace) {
                              return (
                                 <Text key={index} style={{ fontSize: 24, marginHorizontal: 2 }}>
                                    {" "}
                                 </Text>
                              );
                           } else {
                              const inputChar = inputText[inputIndex] || "_";
                              inputIndex++;
                              return (
                                 <Text key={index} style={styles.placeholderText}>
                                    {inputChar}
                                 </Text>
                              );
                           }
                        });
                     })()}
                  </View>
               </View>

               <View>
                  {/* Клавиатура */}
                  <View style={styles.keyboardContainer}>
                     {keyLayout.map((row, rowIndex) => (
                        <View key={`row-${rowIndex}`} style={styles.keyRow}>
                           {row.map((key) => (
                              <TouchableOpacity key={key} style={styles.keyButton} onPress={() => handleKeyPress(key)}>
                                 <Text style={styles.keyText}>{key}</Text>
                              </TouchableOpacity>
                           ))}
                        </View>
                     ))}
                  </View>

                  {/* Кнопка "Далее" */}
                  <View style={{ marginTop: 8, alignItems: "center" }}>
                     <ChoiceRequiredButton
                        title="Далее"
                        handlePress={handleAnswer}
                        isValid={inputText.length === maxInputLength}
                     />
                  </View>
               </View>
            </View>
         </View>
         <View style={{}}>
            {showCorrectAnswer && <LessonAnswerBottom heigth={350} onNext={handlePress} question={question} isCorrect={true} />}
            {showWrongAnswer && <LessonAnswerBottom heigth={350} onNext={handlePress} question={question} isCorrect={false} />}
         </View>
      </View>
   );
}

const styles = StyleSheet.create({
   container: {
      flex: 1,
      height: "100%",
   },
   content: {
      paddingBottom: 0,
      display: "flex",
      flexDirection: "column",
      justifyContent: "space-between",
      height: "78%",
      marginTop: 27,
   },
   placeholderContainer: {
      marginTop: 20,
      alignItems: "center",
   },
   placeholderText: {
      fontSize: 20,
      marginHorizontal: 2,
      color: "#3D3D3D",
   },
   keyboardContainer: {
      marginTop: 20,
      paddingHorizontal: 10,
      backgroundColor: "#E1E1E1",
      borderRadius: 16,
      paddingTop: 8,
      paddingBottom: 23,
   },
   keyRow: {
      flexDirection: "row",
      justifyContent: "center",
      marginBottom: 8,
   },
   keyButton: {
      paddingVertical: 12,
      paddingHorizontal: 8,
      marginHorizontal: 2,
      backgroundColor: "#eee",
      alignItems: "center",
      borderRadius: 4,
      minWidth: 24,
   },
   keyText: {
      fontSize: 16,
      color: "#242B35",
   },
});
