import Svg, { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Defs, Rect } from "react-native-svg";

const AudioLargeSvg = () => (
   <Svg width="200" height="160" viewBox="0 0 25 25" fill="none">
      <Path
         d="M15.2182 5.0475C15.116 4.23018 14.1505 3.84916 13.5177 4.37647L8.28946 8.73333C8.19586 8.81133 8.07788 8.85405 7.95603 8.85405H3.90609C3.18697 8.85405 2.604 9.43701 2.604 10.1561V14.8436C2.604 15.5628 3.18697 16.1457 3.90609 16.1457H7.95603C8.07788 16.1457 8.19586 16.1884 8.28946 16.2664L13.5177 20.6233C14.1505 21.1506 15.116 20.7696 15.2182 19.9523L15.318 19.1538C15.8703 14.7351 15.8703 10.2647 15.318 5.84593L15.2182 5.0475Z"
         fill="#00AFCA"
      />
      <Path
         d="M20.7375 6.19339C21.1472 6.0579 21.5891 6.28016 21.7246 6.68981C22.3292 8.5179 22.6561 10.4715 22.6561 12.4999C22.6561 14.5283 22.3292 16.4819 21.7246 18.31C21.5891 18.7196 21.1472 18.9419 20.7375 18.8064C20.3279 18.6709 20.1056 18.229 20.2411 17.8193C20.794 16.1475 21.0936 14.3595 21.0936 12.4999C21.0936 10.6403 20.794 8.85224 20.2411 7.18044C20.1056 6.7708 20.3279 6.32888 20.7375 6.19339Z"
         fill="#00AFCA"
      />
      <Path
         d="M18.7835 8.27873C18.6347 7.87373 18.1857 7.66605 17.7807 7.81485C17.3757 7.96366 17.168 8.4126 17.3168 8.8176C17.7382 9.96441 17.9686 11.2044 17.9686 12.4999C17.9686 13.5745 17.8101 14.6107 17.5157 15.587C17.4993 15.6414 17.4825 15.6957 17.4652 15.7497C17.4188 15.8952 17.3693 16.0394 17.3168 16.1822C17.168 16.5872 17.3757 17.0361 17.7807 17.1849C18.1857 17.3337 18.6347 17.126 18.7835 16.721C18.8437 16.5572 18.9004 16.3918 18.9538 16.2247C18.9735 16.1627 18.9928 16.1005 19.0117 16.0381C19.3497 14.917 19.5311 13.7289 19.5311 12.4999C19.5311 11.0178 19.2673 9.59558 18.7835 8.27873Z"
         fill="#00AFCA"
      />
   </Svg>
);

export default AudioLargeSvg;
