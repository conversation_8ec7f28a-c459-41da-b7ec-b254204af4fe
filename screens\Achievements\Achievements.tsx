import { Animated, Text, TouchableOpacity, View, Image } from "react-native";
import React, { useRef, useState } from "react";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import QuestsContent from "./QuestContent";
import { QazaqKingdom } from "../../assets/image"; // Подразумевается, что изображение импортировано правильно
import AchievementsContent from "./AchievementsContent";

export default function Achievements() {
   const [selectedTab, setSelectedTab] = useState("tasks");
   const animatedValue = useRef(new Animated.Value(0)).current;

   const handlePress = (tab: string) => {
      setSelectedTab(tab);
      Animated.timing(animatedValue, {
         toValue: tab === "tasks" ? 0 : 1,
         duration: 300,
         useNativeDriver: false,
      }).start();
   };

   const taskColor = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: ["#fff", "#008498"],
   });

   const achievementColor = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: ["#449F2F", "#fff"],
   });

   const backgroundColor = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: ["#4DAF37", "#00AFCA"],
   });

   const height = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [220, 30],
   });

   const imageOpacity = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [1, 0], // Прозрачность от 1 (видимо) до 0 (скрыто)
   });

   return (
      <SafeAreaProvider>
         <Animated.View
            style={{
               width: "100%",
               backgroundColor: backgroundColor, // Анимация фона
            }}
         >
            <SafeAreaView></SafeAreaView>
            <Animated.View
               style={{
                  height: height, // Анимация высоты
                  position: "relative",
               }}
            >
               <View
                  style={{
                     display: "flex",
                     flexDirection: "row",
                     justifyContent: "space-between",
                  }}
               >
                  <TouchableOpacity
                     style={{ width: "50%" }}
                     onPress={() => handlePress("tasks")}
                  >
                     <Animated.View
                        style={{
                           borderBottomColor: taskColor,
                           borderBottomWidth: 2,
                        }}
                     >
                        <Animated.Text
                           style={{
                              fontWeight: "600",
                              fontSize: 16,
                              color: taskColor,
                              textAlign: "center",
                              marginBottom: 9,
                           }}
                        >
                           Задания
                        </Animated.Text>
                     </Animated.View>
                  </TouchableOpacity>

                  <TouchableOpacity
                     style={{ width: "50%" }}
                     onPress={() => handlePress("achievements")}
                  >
                     <Animated.View
                        style={{
                           borderBottomColor: achievementColor,
                           borderBottomWidth: 2,
                        }}
                     >
                        <Animated.Text
                           style={{
                              fontWeight: "600",
                              fontSize: 16,
                              color: achievementColor,
                              textAlign: "center",
                              marginBottom: 9,
                           }}
                        >
                           Достижения
                        </Animated.Text>
                     </Animated.View>
                  </TouchableOpacity>
               </View>

               <Text
                  style={{
                     color: "#fff",
                     fontWeight: 600,
                     fontSize: 20,
                     lineHeight: 24,
                     maxWidth: 210,
                     marginHorizontal: 15,
                     marginTop: 31,
                  }}
               >
                  Поднимайтесь в рейтинге выполняя задания
               </Text>

               <Animated.Image
                  style={{
                     position: "absolute",
                     bottom: 0,
                     right: 0,
                     resizeMode: "contain", // Чтобы сохранить пропорции
                     opacity: imageOpacity, // Анимация исчезновения
                  }}
                  source={QazaqKingdom}
               />
            </Animated.View>
         </Animated.View>
         <StatusBar />

         {selectedTab == "tasks" ? <QuestsContent /> : <AchievementsContent />}
      </SafeAreaProvider>
   );
}
