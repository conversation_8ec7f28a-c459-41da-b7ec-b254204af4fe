import Svg, { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Defs, Rect } from "react-native-svg";

const CrosswordSvg = () => (
   <Svg width="47" height="46" viewBox="0 0 47 46" fill="none">
      <Path
         d="M35.7855 10.666L30.4956 9.62915L30.8047 8.05126C31.4411 4.80443 29.3248 1.6568 26.0781 1.02035L24.9025 0.789867C21.6557 0.153779 18.508 2.26974 17.8715 5.51654L17.5623 7.09443L12.2724 6.05776C9.02566 5.42136 5.87788 7.53763 5.24158 10.7843L4.30223 15.5773L7.89842 16.2821C11.1452 16.9185 13.2613 20.0662 12.625 23.3129L12.3946 24.4886C11.7582 27.7355 8.61062 29.8516 5.36364 29.2152L1.76768 28.5104L0.828326 33.3034C0.192024 36.5501 2.30783 39.6978 5.55469 40.334L10.8446 41.3709L11.5114 37.9687C12.1479 34.7221 15.2956 32.606 18.5422 33.2421L19.7179 33.4724C22.9643 34.1089 25.0806 37.2567 24.4444 40.5034L23.7778 43.9055L29.0677 44.9425C32.3143 45.5784 35.4622 43.4622 36.0985 40.2157L37.0378 35.4226L39.0681 35.8208C42.3149 36.457 45.4627 34.3409 46.0991 31.0941L46.3295 29.9184C46.9658 26.6718 44.8497 23.5239 41.6029 22.8876L39.5725 22.4898L40.512 17.6968C41.1485 14.4501 39.0322 11.3022 35.7855 10.666Z"
         fill="#3290FF"
      />
   </Svg>
);

export default CrosswordSvg;
