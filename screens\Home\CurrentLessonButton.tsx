import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import React from "react";
import BookSvg from "../../assets/svg/BookSvg";
import { useNavigation } from "@react-navigation/native";

export default function CurrentLessonButton() {
   const { navigate } = useNavigation();
   return (
      <TouchableOpacity
         style={styles.container}
         onPress={() => {
            // @ts-ignore
            navigate("Theory");
         }}
      >
         <View>
            <View
               style={{
                  marginTop: 20,
                  marginLeft: 16,
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "space-between",
                  height: 51,
               }}
            >
               <Text style={{ fontWeight: 500, fontSize: 14, color: "#242B35" }}>Теория - Курс 1</Text>
               <Text style={{ fontWeight: 700, fontSize: 18, color: "#242B35" }}>Изучите базовые фразы</Text>
            </View>
         </View>
         <View
            style={{
               height: "100%",
               borderLeftColor: "#ED8A19",
               borderLeftWidth: 1.5,
            }}
         >
            <View
               style={{
                  marginVertical: "auto",
                  paddingLeft: 10,
                  paddingRight: 20,
               }}
            >
               <BookSvg />
            </View>
         </View>
      </TouchableOpacity>
   );
}

const styles = StyleSheet.create({
   container: {
      width: "85%",
      height: 91,
      borderRadius: 10,
      backgroundColor: "#F1C644",
      marginHorizontal: "auto",
      marginTop: 24,
      shadowColor: "#ED8A19",
      shadowOffset: { width: 0, height: 4 }, // Смещение тени
      shadowOpacity: 1, // Прозрачность тени
      shadowRadius: 0, // Радиус размытия
      elevation: 4, // Только для Android
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
   },
});
